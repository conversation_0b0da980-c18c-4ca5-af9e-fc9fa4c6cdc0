# Technology Stack

## Core Development
- **Language:** Java 21 (LTS) - Utilizing modern features for robust and efficient service development.
- **Framework:** Quarkus - High-performance, Kubernetes-native Java stack with a focus on developer joy and low resource usage.
- **Build System:** Gradle - Used for dependency management, builds, and lifecycle automation.

## Persistence and Data
- **Database:** PostgreSQL - Enterprise-grade relational database.
- **ORM / Persistence:** Hibernate Reactive with Panache - Providing a non-blocking, asynchronous layer for database operations.
- **Migrations:** Flyway - Handles schema evolution and versioned database migrations.

## Communication and APIs
- **GraphQL:** SmallRye GraphQL - Primary interface for hierarchical data retrieval and complex queries.
- **REST APIs:** Quarkus REST (with <PERSON>) - Used for standard resource management and integration points.
- **WebSockets:** Quarkus WebSockets Next - Real-time communication channel for live station monitoring.
- **Security:** OIDC & Security JPA Reactive - Centralized identity management and secure, reactive access control.

## Specialized Libraries
- **Reporting:** Apache POI (Excel generation) and Apache PDFBox (PDF generation) for professional documentation.
- **Algorithms:** ojAlgo - Used for mathematical programming and optimization tasks (e.g., smart charging calculations).
- **Standards:** Custom implementation for OCPP (Open Charge Point Protocol) 1.6 and 2.0.1.

## Infrastructure and Observability
- **Containerization:** Docker - Facilitates consistent deployment across environments.
- **Observability:** OpenTelemetry - Standardized telemetry for tracing and performance monitoring.
- **Testing:** JUnit 5, Rest Assured, and Quarkus Test extensions for comprehensive unit and integration testing.
