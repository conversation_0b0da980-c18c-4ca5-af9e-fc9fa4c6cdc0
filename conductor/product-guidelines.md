# Product Guidelines

## Prose Style and Tone
- **Technical and Precise:** Documentation and system messaging must favor accuracy and data integrity. Avoid ambiguous language. Use standard industry terminology (e.g., OCPP, EVSE, Load Balancing) consistently.
- **Performance-Oriented:** Communication should reflect the system's high-performance nature, focusing on real-time data delivery and system responsiveness.
- **Authoritative Data Presentation:** Reports and alerts must provide clear, unambiguous metrics, ensuring that technical operators have the precise information needed for decision-making.

## Visual Identity
- **Data Density and Clarity:** UI and reports (especially Excel/PDF) must prioritize information density without sacrificing readability. Use structured tables, clear headers, and logical groupings of data.
- **Structured Metrics:** Prioritize the display of technical metrics (kW, kWh, Voltage, Amperage) in a way that allows for rapid scanning and comparison.
- **Functional Aesthetics:** The design should feel modern and high-tech, using a clean, professional color palette that emphasizes readability and data contrast.

## Brand Messaging
- **Energy Intelligence:** Position Smart Charge as an "intelligent" system that proactively manages power. Highlight the benefits of smart load balancing and grid optimization in all high-level communications.
- **Optimized Distribution:** Focus on the system's ability to maximize infrastructure utility through smart algorithms and hierarchical asset management.
- **Reliable Innovation:** Convey a sense of cutting-edge technology that is nonetheless rock-solid and enterprise-ready.
