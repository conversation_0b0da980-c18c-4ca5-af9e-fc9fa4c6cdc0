{"track_id": "optimize_session_fetch_20260105", "type": "feature", "status": "new", "created_at": "2026-01-05T09:34:00Z", "updated_at": "2026-01-05T09:34:00Z", "description": "In SessionServiceImpl class, the getEnrichedSessions method can be improved: First collect all the ids from the sessions (charging station id, user id, evse id) and use QueryFilter.addInFilter() to fetch only these entities to get their names for the enriched session. This helps not to fetch all evses, users and charging stations from the organization or in case of evse in the whole system, only those which will be in the report."}