# Specification: Optimize Entity Fetching in SessionServiceImpl

## 1. Overview
The `getEnrichedSessions` method in `SessionServiceImpl` currently retrieves entities (Charging Stations, Users, and EVSEs) inefficiently, potentially fetching all records associated with an organization or the entire system. This optimization aims to reduce database load and memory usage by fetching only the specific entities referenced by the sessions being processed.

## 2. Functional Requirements
1.  **Modify `getEnrichedSessions`**:
    *   Iterate through the target list of `Session` objects.
    *   Collect unique identifiers for the following entities:
        *   Charging Stations
        *   Users
        *   EVSEs
2.  **Implement `QueryFilter`**:
    *   Utilize `QueryFilter.addInFilter()` to construct specific queries for the respective repositories.
    *   Ensure the queries restrict results to *only* the IDs collected in step 1.
3.  **Entity Retrieval**:
    *   Replace existing broad fetch logic with the filtered queries.
    *   Map the retrieved entities back to the sessions to complete the enrichment process.

## 3. Non-Functional Requirements
*   **Performance**: Significantly reduce the number of entities instantiated and rows fetched from the database during report generation.
*   **Code Style**: Adhere to existing project patterns regarding `QueryFilter` usage.

## 4. Acceptance Criteria
*   The `getEnrichedSessions` method successfully enriches sessions with correct Charging Station, User, and EVSE names.
*   **Verification**: Manual testing confirms that the application generates reports correctly without errors. Inspection of logs (or debug execution) confirms that queries are filtered by specific IDs rather than fetching broad sets (e.g., entire Organization lists).

## 5. Out of Scope
*   Optimization of other entity fetches (e.g., Locations, Tenants).
*   Creation of automated integration tests to assert query counts (verification will be manual).