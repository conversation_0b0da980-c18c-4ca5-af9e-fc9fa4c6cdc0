plugins {
    id 'java'
    id 'io.quarkus'
}

repositories {
    mavenCentral()
    mavenLocal()
}

def quarkusPlatformGroupId = project.property("quarkusPlatformGroupId")
def quarkusPlatformArtifactId = project.property("quarkusPlatformArtifactId")
def quarkusPlatformVersion = project.property("quarkusPlatformVersion")

dependencies {
    implementation 'io.quarkus:quarkus-awt'
    implementation 'io.quarkus:quarkus-opentelemetry'
    implementation "io.quarkus:quarkus-container-image-docker"

    // OIDC
    implementation "io.quarkus:quarkus-oidc-client"

    // Hibernate Reactive + PostgreSQL Reactive
    implementation "io.quarkus:quarkus-reactive-pg-client"
    implementation "io.quarkus:quarkus-hibernate-reactive-panache"

    // JPA Metamodel Generator
    annotationProcessor "org.hibernate:hibernate-jpamodelgen:6.4.4.Final"

    // Flyway
    implementation "io.quarkus:quarkus-flyway"
    // JDBC
    implementation "io.quarkus:quarkus-jdbc-postgresql"

    // WebSockets Next
    implementation "io.quarkus:quarkus-websockets-next"

    // GraphQL
    implementation "io.quarkus:quarkus-smallrye-graphql"

    // REST
    implementation "io.quarkus:quarkus-smallrye-openapi"
    implementation "io.quarkus:quarkus-rest"
    implementation "io.quarkus:quarkus-rest-jackson"
    implementation "io.quarkus:quarkus-rest-client-jackson"

    // Validation
    implementation "io.quarkus:quarkus-hibernate-validator"

    // Quarkus Platform BOM
    implementation enforcedPlatform("${quarkusPlatformGroupId}:${quarkusPlatformArtifactId}:${quarkusPlatformVersion}")
    implementation "io.quarkus:quarkus-arc"

    // Security JPA Reactive
    implementation "io.quarkus:quarkus-security-jpa-reactive"

    // Apache POI
    implementation "io.quarkiverse.poi:quarkus-poi:2.2.0"

    // Apache PDFBox
    implementation "io.quarkiverse.pdfbox:quarkus-pdfbox:1.2.0"

    // Common libs
    implementation "org.apache.commons:commons-text"
    implementation "org.ojalgo:ojalgo:56.0.0"

    // Test
    testImplementation "io.quarkus:quarkus-junit5"
    testImplementation "io.quarkus:quarkus-junit5-mockito"
    testImplementation "io.rest-assured:rest-assured"
    testImplementation "org.junit.jupiter:junit-jupiter-engine"
    testImplementation "io.quarkus:quarkus-websockets"
    testImplementation "com.fasterxml.jackson.datatype:jackson-datatype-jsr310"
    testImplementation "io.quarkus:quarkus-test-vertx"
    testImplementation "io.quarkus:quarkus-test-hibernate-reactive-panache"
}

group = "org.galiasystems"
version = "1.0.14"

java {
    sourceCompatibility = JavaVersion.VERSION_21
    targetCompatibility = JavaVersion.VERSION_21
}

tasks.withType(Test) {
    systemProperty "java.util.logging.manager", "org.jboss.logmanager.LogManager"
    useJUnitPlatform()
    testLogging {
        showStandardStreams = true
    }
    jvmArgs "--add-opens", "java.base/java.lang=ALL-UNNAMED"
}

tasks.withType(JavaCompile) {
    options.encoding = "UTF-8"
    options.compilerArgs += "-parameters"

    // Annotation processing
    options.annotationProcessorPath = configurations.annotationProcessor

    // Generated sources output
    options.generatedSourceOutputDirectory = file("$buildDir/generated/sources/annotationProcessor/java/main")
}

sourceSets {
    main {
        java {
            srcDir("$buildDir/generated/sources/annotationProcessor/java/main")
        }
    }
}

tasks.register("generateJpaMetamodel") {
    group = "build"
    description = "Generate JPA metamodel classes"

    dependsOn "compileJava"

    doLast {
        println "JPA metamodel classes generated in: ${buildDir}/generated/sources/annotationProcessor/java/main"
    }
}