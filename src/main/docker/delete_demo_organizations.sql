CREATE OR REPLACE PROCEDURE delete_demo_organizations(
    p_days INTEGER
)
LANGUAGE plpgsql
AS $$
DECLARE
  org RECORD;
BEGIN
  FOR org IN
    SELECT id
    FROM organization
    WHERE licence_type = 'Demo' 
		and create_date_time < now() - make_interval(days => p_days)
  LOOP
	BEGIN

	  DELETE FROM access_group_charging_station
      WHERE access_group_id in (SELECT ag.id
	    					    FROM access_group ag
	    		                WHERE ag.organization_id = org.id);

	  DELETE FROM access_group_csms_user
      WHERE access_group_id in (SELECT ag.id
	    					    FROM access_group ag
	    		                WHERE ag.organization_id = org.id);

	  DELETE FROM access_group_location
      WHERE access_group_id in (SELECT ag.id
	    					    FROM access_group ag
	    		                WHERE ag.organization_id = org.id);

	  DELETE FROM access_group_power_grid
      WHERE access_group_id in (SELECT ag.id
	    					    FROM access_group ag
	    		                WHERE ag.organization_id = org.id);

	  DELETE FROM access_group
      WHERE organization_id = org.id;

      DELETE FROM report
      WHERE charging_station_id in (SELECT cs.id
	    					  		FROM charging_station cs
									  LEFT JOIN power_grid pg on pg.id = cs.power_grid_id
									  LEFT JOIN location loc on loc.id = pg.location_id
	    		                    WHERE loc.organization_id = org.id);

      DELETE FROM charging_station_variable_value
      WHERE charging_station_variable_id in (SELECT v.id
	    					  		         FROM charging_station_variable v
									           LEFT JOIN charging_station cs on cs.id = v.charging_station_id
									           LEFT JOIN power_grid pg on pg.id = cs.power_grid_id
									           LEFT JOIN location loc on loc.id = pg.location_id
	    		                             WHERE loc.organization_id = org.id);

      DELETE FROM meter_value
      WHERE charging_station_id in (SELECT cs.id
	    					  		FROM charging_station cs
									  LEFT JOIN power_grid pg on pg.id = cs.power_grid_id
									  LEFT JOIN location loc on loc.id = pg.location_id
	    		                    WHERE loc.organization_id = org.id);

	  DELETE FROM charging_station_variable
      WHERE charging_station_id in (SELECT cs.id
	    					  		FROM charging_station cs
									  LEFT JOIN power_grid pg on pg.id = cs.power_grid_id
									  LEFT JOIN location loc on loc.id = pg.location_id
	    		                    WHERE loc.organization_id = org.id);

	  DELETE FROM session
      WHERE charging_station_id in (SELECT cs.id
	    					  		FROM charging_station cs
									  LEFT JOIN power_grid pg on pg.id = cs.power_grid_id
									  LEFT JOIN location loc on loc.id = pg.location_id
	    		                    WHERE loc.organization_id = org.id);

	  DELETE FROM evse_transaction_event
      WHERE transaction_id in (SELECT t.id
					    	   FROM evse_transaction t
							     LEFT JOIN evse e on e.id = t.evse_id
						  		 LEFT JOIN charging_station cs on cs.id = e.charging_station_id
						  		 LEFT JOIN power_grid pg on pg.id = cs.power_grid_id
						  		 LEFT JOIN location loc on loc.id = pg.location_id
	    		        	   WHERE loc.organization_id = org.id);

	  DELETE FROM evse_transaction
      WHERE evse_id in (SELECT e.id
					    FROM evse e
						  LEFT JOIN charging_station cs on cs.id = e.charging_station_id
						  LEFT JOIN power_grid pg on pg.id = cs.power_grid_id
						  LEFT JOIN location loc on loc.id = pg.location_id
	    		        WHERE loc.organization_id = org.id);

	  DELETE FROM connector
      WHERE evse_id in (SELECT e.id
					    FROM evse e
						  LEFT JOIN charging_station cs on cs.id = e.charging_station_id
						  LEFT JOIN power_grid pg on pg.id = cs.power_grid_id
						  LEFT JOIN location loc on loc.id = pg.location_id
	    		        WHERE loc.organization_id = org.id);

      DELETE FROM evse
      WHERE charging_station_id in (SELECT cs.id
	    					  		FROM charging_station cs
									  LEFT JOIN power_grid pg on pg.id = cs.power_grid_id
									  LEFT JOIN location loc on loc.id = pg.location_id
	    		                    WHERE loc.organization_id = org.id);

	  DELETE FROM charging_station_access
      WHERE charging_station_id in (SELECT cs.id
	    					  		FROM charging_station cs
									  LEFT JOIN power_grid pg on pg.id = cs.power_grid_id
									  LEFT JOIN location loc on loc.id = pg.location_id
	    		                    WHERE loc.organization_id = org.id);

	  DELETE FROM charging_station
	  WHERE id in (SELECT cs.id
				   FROM charging_station cs
				     LEFT JOIN power_grid pg on pg.id = cs.power_grid_id
					 LEFT JOIN location loc on loc.id = pg.location_id
				   WHERE loc.organization_id = org.id);

	  DELETE FROM power_grid_access
      WHERE power_grid_id in (SELECT pg.id
					          FROM power_grid pg
					            LEFT JOIN location loc on loc.id = pg.location_id
					          WHERE loc.organization_id = org.id);

	  DELETE FROM power_grid
      WHERE id in (SELECT pg.id
				   FROM power_grid pg
				     LEFT JOIN location loc on loc.id = pg.location_id
				   WHERE loc.organization_id = org.id);
					 
	  DELETE FROM location_access
      WHERE location_id in (SELECT loc.id
	    					FROM location loc
	    		            WHERE loc.organization_id = org.id);

	  DELETE FROM floor
	  WHERE location_id in (SELECT loc.id
	    					FROM location loc
	    		            WHERE loc.organization_id = org.id);

	  DELETE FROM location
      WHERE organization_id = org.id;
    
	  UPDATE csms_user 
	  SET selected_organization_id = null 
	  WHERE selected_organization_id = org.id;
	  
      DELETE FROM organization_access
      WHERE organization_id = org.id;

      DELETE FROM organization
      WHERE id = org.id;

    EXCEPTION
      WHEN OTHERS THEN
        RAISE WARNING 'Error during delete of the demo organization with id=%: %', org.id, SQLERRM;
    END;
  END LOOP;
END;
$$;
