receivers:
  otlp:
    protocols:
      grpc:
        endpoint: 0.0.0.0:4317
      http:
        endpoint: 0.0.0.0:4318

exporters:
  debug:
  prometheus:
    endpoint: 0.0.0.0:8889
  file/traces:
    path: /tmp/otel_logs/traces.jsonl
    rotation:
      max_megabytes: 100
      max_days: 7
      max_backups: 3
  file/metrics:
    path: /tmp/otel_logs/metrics.jsonl
    rotation:
      max_megabytes: 100
      max_days: 7
      max_backups: 3
  file/logs:
    path: /tmp/otel_logs/logs.jsonl
    rotation:
      max_megabytes: 100
      max_days: 7
      max_backups: 3

processors:
  batch:

extensions:
  health_check:
    endpoint: 0.0.0.0:13133
  pprof:
    endpoint: 0.0.0.0:1888
  zpages:
    endpoint: 0.0.0.0:55679

service:
  extensions: [health_check, pprof, zpages]
  pipelines:
    traces:
      receivers: [otlp]
      processors: [batch]
      exporters: [debug, file/traces]
    metrics:
      receivers: [otlp]
      processors: [batch]
      exporters: [prometheus, debug, file/metrics]
    logs:
      receivers: [otlp]
      processors: [batch]
      exporters: [debug, file/logs]