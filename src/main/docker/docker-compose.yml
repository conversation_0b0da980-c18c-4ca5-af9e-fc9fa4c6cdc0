services:
  smart-charge-backend:
    image: smart-charge-backend:latest
    ports:
      - "8443:8443"
    environment:
      - QUARKUS_DATASOURCE_JDBC_URL=******************************************************************
      - QUARKUS_DATASOURCE_REACTIVE_URL=vertx-reactive:postgresql://postgres/smart_charge?currentSchema=smart_charge
      - QUARKUS_DATASOURCE_USERNAME=smart_charge
      - QUARKUS_DATASOURCE_PASSWORD=smart_charge
      - OCPP_METRICS_ROLLING_WINDOW_SECONDS=10

      - QUARKUS_OTEL_EXPORTER_OTLP_ENDPOINT=http://demo.auth.smartcharge.it.com:4317
      - QUARKUS_OIDC_CLIENT_AUTH_SERVER_URL=https://demo.auth.smartcharge.it.com/realms/flutter-app/
      - QUARKUS_OIDC_CLIENT_ID=flutter-client
      - QUARKUS_OIDC_CLIENT_CREDENTIALS_SECRET=wanLJe9K8GIowpzKCAtsM56ydPcseBnb
      - QUARKUS_REST_CLIENT_CUSTOM_KEYCLOAK_URL=https://demo.auth.smartcharge.it.com/realms/flutter-app
      - CSMS_OAUTH2_AUTHORIZATION_ENDPOINT=https://demo.auth.smartcharge.it.com/realms/flutter-app/protocol/openid-connect/auth/
      - CSMS_OAUTH2_REGISTRATION_ENDPOINT=https://demo.auth.smartcharge.it.com/realms/flutter-app/protocol/openid-connect/registrations/
      - CSMS_OAUTH2_SESSION_ID_COOKIE_DOMAIN=demo.app.smartcharge.it.com
      - CSMS_OAUTH2_ACCESS_TOKEN_COOKIE_DOMAIN=demo.app.smartcharge.it.com
      - CSMS_OAUTH2_REFRESH_TOKEN_COOKIE_DOMAIN=demo.app.smartcharge.it.com
      - CSMS_WS_URL_BASE=wss://demo.app.smartcharge.it.com:8443/websocket/ocpp


    depends_on:
      postgres:
        condition: service_healthy

  postgres:
    image: postgres:17
    environment:
      - POSTGRES_DB=smart_charge
      - POSTGRES_USER=smart_charge
      - POSTGRES_PASSWORD=smart_charge
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-schemas.sql:/docker-entrypoint-initdb.d/init-schemas.sql
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -U smart_charge -d smart_charge" ]
      interval: 10s
      timeout: 5s
      retries: 5
  smart-charge-ui:
    image: smart-charge-flutter-client:latest
    container_name: smart-charge-flutter-client
    expose:
      - "443"
    environment:
      - BACKEND_URL=https://demo.app.smartcharge.it.com:8443/graphql

    networks:
      - app-network

volumes:
  postgres_data:

networks:
  app-network:
    driver: bridge
