#!/usr/bin/env bash
set -euo pipefail


DEFAULT_PGDATABASE="smart_charge"
DEFAULT_PGUSER="smart_charge"
DEFAULT_PGSCHEMA="public"
DEFAULT_DAYS="7"

usage() {
  cat <<EOF
Usage: $0 <container_name> [database_name] [db_user] [db_schema] [days]

  <container_name>    Required. The Docker container name or ID where the PostgreSQL instance runs.
  [database_name]     Optional. The database name inside the container. Default: ${DEFAULT_PGDATABASE}
  [db_user]           Optional. The database user name. Default: ${DEFAULT_PGUSER}
  [db_schema]         Optional. The database schema. Default: ${DEFAULT_PGSCHEMA}
  [days]              Optional. The days parameter of the stored procedure. Default: ${DEFAULT_DAYS}

Examples:
  $0 my_container
  $0 my_container custom_db
  $0 my_container custom_db custom_user
  $0 my_container custom_db custom_user custom_schema
  $0 my_container custom_db custom_user custom_schema custom_days

Options:
  -h, --help          Show this help message and exit.
EOF
}


if [[ "${1:-}" == "--help" || "${1:-}" == "-h" ]]; then
  usage
  exit 0
fi

if [ -z "${1:-}" ]; then
  echo "Error: cont­ainer name is required."
  usage
  exit 1
fi

CONTAINER_NAME="$1"
PGDATABASE="${2:-$DEFAULT_PGDATABASE}"
PGUSER="${3:-$DEFAULT_PGUSER}"
PGSCHEMA="${4:-$DEFAULT_PGSCHEMA}"
DAYS="${5:-$DEFAULT_DAYS}"

SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

LOCAL_PROC_SQL_FILE="${SCRIPT_DIR}/delete_demo_organizations.sql"

if [[ ! -f "$LOCAL_PROC_SQL_FILE" ]]; then
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] Error: SQL file not found: $LOCAL_PROC_SQL_FILE"
  exit 1
fi

echo "[$(date '+%Y-%m-%d %H:%M:%S')] Using container: $CONTAINER_NAME"
echo "[$(date '+%Y-%m-%d %H:%M:%S')] Database: $PGDATABASE"
echo "[$(date '+%Y-%m-%d %H:%M:%S')] User: $PGUSER"
echo "[$(date '+%Y-%m-%d %H:%M:%S')] Schema: $PGSCHEMA"
echo "[$(date '+%Y-%m-%d %H:%M:%S')] Days: $DAYS"

docker cp "$LOCAL_PROC_SQL_FILE" "${CONTAINER_NAME}:/tmp/delete_demo_organizations.sql"

docker exec -i "$CONTAINER_NAME" bash -lc "
psql -U ${PGUSER} -d ${PGDATABASE} -f /tmp/delete_demo_organizations.sql &&
psql -U ${PGUSER} -d ${PGDATABASE} -c \"call ${PGSCHEMA}.delete_demo_organizations(${DAYS});\"
"

echo "[$(date '+%Y-%m-%d %H:%M:%S')] Kész."
