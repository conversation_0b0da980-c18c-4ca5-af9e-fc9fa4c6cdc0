version: "3.9"

services:
  otel-collector:
    image: otel/opentelemetry-collector-contrib:0.141.0
    container_name: otel-collector
    user: "0"
    volumes:
      - ./otel-collector-config.yaml:/etc/otelcol-contrib/config.yaml:ro
      - ./otel_collector_logs:/tmp/otel_logs
    ports:
      - 1888:1888   # pprof extension
      - 8890:8888   # Prometheus metrics exposed by the Collector
      - 8889:8889   # Prometheus exporter metrics
      - 13133:13133 # health_check extension
      - 4317:4317   # OTLP gRPC receiver
      - 4318:4318   # OTLP HTTP receiver
      - 55679:55679 # zpages extension

#  prometheus:
#    image: prom/prometheus
#    volumes:
#      - ./prometheus.yml:/etc/prometheus/prometheus.yml
#    ports:
#      - 9090:9090