####
# This Dockerfile is used in order to build a container that runs the Quarkus application in native (no JVM) mode.
#
# Before building the container image run:
#
# ./gradlew build -Dquarkus.package.type=native
#
# Then, build the image with:
#
# docker build -f src/main/docker/Dockerfile.native -t quarkus/smart-charge .
#
# Then run the container using:
#
# docker run -i --rm -p 8080:8080 quarkus/smart-charge
#
###
FROM registry.access.redhat.com/ubi9/ubi-minimal:9.5

######################### Set up environment for POI #############################
RUN microdnf update -y && microdnf install -y freetype fontconfig && microdnf clean all
######################### Set up environment for POI #############################

WORKDIR /work/
RUN chown 1001 /work \
    && chmod "g+rwX" /work \
    && chown 1001:root /work
# Shared objects to be dynamically loaded at runtime as needed,
COPY --chown=1001:root --chmod=0755 build/*.properties build/*.so /work/
COPY --chown=1001:root --chmod=0755 build/*-runner /work/application
# Permissions fix for Windows
RUN chmod "ugo+x" /work/application

EXPOSE 8080
EXPOSE 8443
USER 1001

ENTRYPOINT ["./application", "-Dquarkus.http.host=0.0.0.0"]