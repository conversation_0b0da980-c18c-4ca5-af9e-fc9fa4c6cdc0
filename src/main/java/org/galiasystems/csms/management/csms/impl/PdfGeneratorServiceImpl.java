package org.galiasystems.csms.management.csms.impl;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.font.PDFont;
import org.apache.pdfbox.pdmodel.font.PDType1Font;
import org.apache.pdfbox.pdmodel.font.Standard14Fonts;
import org.galiasystems.csms.management.csms.PdfGeneratorService;
import org.galiasystems.csms.management.model.Session;
import org.galiasystems.csms.management.model.SessionReportEntry;

import io.smallrye.mutiny.Uni;
import io.smallrye.mutiny.infrastructure.Infrastructure;
import jakarta.enterprise.context.ApplicationScoped;

@ApplicationScoped
public class PdfGeneratorServiceImpl implements PdfGeneratorService {
    
    private static final DateTimeFormatter DTF = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    // Column weights (total 100%)
    private static final float[] COLUMN_WEIGHTS = {0.15f, 0.08f, 0.08f, 0.25f, 0.12f, 0.12f, 0.09f, 0.11f};

    @Override
    public Uni<byte[]> generateSessionReport(final List<SessionReportEntry> sessions, final String generatedBy, final String organizationName) {

        return Uni.createFrom().item(() -> {
            try (final PDDocument document = new PDDocument()) {
                final float margin = 50;
                final float rowHeight = 20f;
                final float cellMargin = 5f;
                
                PDPage page = new PDPage();
                document.addPage(page);
                
                final float tableWidth = page.getMediaBox().getWidth() - 2 * margin;
                final float[] colWidths = new float[COLUMN_WEIGHTS.length];
                for (int i = 0; i < COLUMN_WEIGHTS.length; i++) {
                    colWidths[i] = tableWidth * COLUMN_WEIGHTS[i];
                }
                
                float currentY = page.getMediaBox().getHeight() - margin;

                try (PDPageContentStream cs = new PDPageContentStream(document, page)) {
                    // Title
                    cs.beginText();
                    cs.setFont(new PDType1Font(Standard14Fonts.FontName.HELVETICA_BOLD), 16);
                    cs.newLineAtOffset(margin, currentY);
                    cs.showText("Charging Session Report");
                    cs.endText();
                    currentY -= 30;

                    // Metadata
                    cs.setFont(new PDType1Font(Standard14Fonts.FontName.HELVETICA), 10);
                    
                    cs.beginText();
                    cs.newLineAtOffset(margin, currentY);
                    cs.showText("Organization: " + (organizationName != null ? organizationName : "N/A"));
                    cs.endText();
                    currentY -= 15;

                    cs.beginText();
                    cs.newLineAtOffset(margin, currentY);
                    cs.showText("Generated By: " + (generatedBy != null ? generatedBy : "N/A"));
                    cs.endText();
                    currentY -= 15;

                    cs.beginText();
                    cs.newLineAtOffset(margin, currentY);
                    cs.showText("Generated At: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                    cs.endText();
                    currentY -= 30;

                    // Table Header
                    currentY -= drawTableHeader(cs, margin, currentY, tableWidth, colWidths, rowHeight, cellMargin);

                    BigDecimal totalEnergy = BigDecimal.ZERO;
                    
                    final PDFont contentFont = new PDType1Font(Standard14Fonts.FontName.HELVETICA);
                    final float contentFontSize = 8;

                    PDPageContentStream activeCs = cs;
                    int rowCount = 0;
                    for (final SessionReportEntry entry : sessions) {
                        rowCount++;
                        final Session session = entry.session();
                        totalEnergy = totalEnergy.add(session.getKwh());

                        final List<List<String>> wrappedCells = new ArrayList<>();
                        wrappedCells.add(wrapText(entry.chargingStationName(), contentFont, contentFontSize, colWidths[0] - 2 * cellMargin));
                        wrappedCells.add(wrapText(entry.evseName(), contentFont, contentFontSize, colWidths[1] - 2 * cellMargin));
                        wrappedCells.add(wrapText(session.getEvseConnectorId().map(Object::toString).orElse(""), contentFont, contentFontSize, colWidths[2] - 2 * cellMargin));
                        wrappedCells.add(wrapText(entry.userName(), contentFont, contentFontSize, colWidths[3] - 2 * cellMargin));
                        wrappedCells.add(wrapText(session.getStartDateTime().format(DTF), contentFont, contentFontSize, colWidths[4] - 2 * cellMargin));
                        wrappedCells.add(wrapText(session.getEndDateTime().map(dt -> dt.format(DTF)).orElse(""), contentFont, contentFontSize, colWidths[5] - 2 * cellMargin));
                        wrappedCells.add(wrapText(session.getKwh().setScale(2, RoundingMode.HALF_UP).toString(), contentFont, contentFontSize, colWidths[6] - 2 * cellMargin));
                        wrappedCells.add(wrapText(session.getStatus().name(), contentFont, contentFontSize, colWidths[7] - 2 * cellMargin));

                        int maxLines = 1;
                        for (final List<String> lines : wrappedCells) {
                            maxLines = Math.max(maxLines, lines.size());
                        }

                        final float dynamicRowHeight = Math.max(rowHeight, maxLines * (contentFontSize + 2) + 10);

                        if (currentY - dynamicRowHeight < margin) {
                            activeCs.close();
                            page = new PDPage();
                            document.addPage(page);
                            activeCs = new PDPageContentStream(document, page);
                            currentY = page.getMediaBox().getHeight() - margin;
                            currentY -= drawTableHeader(activeCs, margin, currentY, tableWidth, colWidths, rowHeight, cellMargin);
                        }

                        final boolean isZebra = rowCount % 2 == 0;
                        if (isZebra) {
                            activeCs.setNonStrokingColor(153/255f, 204/255f, 255/255f);
                            activeCs.addRect(margin, currentY - dynamicRowHeight, tableWidth, dynamicRowHeight);
                            activeCs.fill();
                            activeCs.setNonStrokingColor(0, 0, 0);
                        }

                        activeCs.setFont(contentFont, contentFontSize);
                        drawRow(activeCs, margin, currentY, colWidths, dynamicRowHeight, cellMargin, contentFontSize, wrappedCells);
                        
                        currentY -= dynamicRowHeight;
                    }

                    // Summary Section
                    if (currentY - 60 < margin) {
                        activeCs.close();
                        page = new PDPage();
                        document.addPage(page);
                        activeCs = new PDPageContentStream(document, page);
                        currentY = page.getMediaBox().getHeight() - margin;
                    } else {
                        currentY -= 20; // Gap
                    }

                    activeCs.setFont(new PDType1Font(Standard14Fonts.FontName.HELVETICA_BOLD), 12);
                    activeCs.beginText();
                    activeCs.newLineAtOffset(margin, currentY);
                    activeCs.showText("Summary");
                    activeCs.endText();
                    currentY -= 20;

                    activeCs.setFont(new PDType1Font(Standard14Fonts.FontName.HELVETICA), 10);
                    activeCs.beginText();
                    activeCs.newLineAtOffset(margin, currentY);
                    activeCs.showText("Total Energy: " + totalEnergy.setScale(2, RoundingMode.HALF_UP) + " kWh");
                    activeCs.endText();
                    currentY -= 15;

                    activeCs.beginText();
                    activeCs.newLineAtOffset(margin, currentY);
                    activeCs.showText("Session Count: " + sessions.size());
                    activeCs.endText();
                    
                    activeCs.close();
                }

                final ByteArrayOutputStream os = new ByteArrayOutputStream();
                document.save(os);
                return os.toByteArray();
            } catch (final IOException e) {
                throw new RuntimeException("Pdf generation error!", e);
            }
        }).runSubscriptionOn(Infrastructure.getDefaultExecutor());
    }

    private float drawTableHeader(final PDPageContentStream cs, final float margin, final float y, 
                                final float tableWidth, final float[] colWidths, final float rowHeight, 
                                final float cellMargin) throws IOException {
        final org.apache.pdfbox.pdmodel.font.PDFont headerFont = new PDType1Font(Standard14Fonts.FontName.HELVETICA_BOLD);
        final float headerFontSize = 10;
        cs.setFont(headerFont, headerFontSize);
        
        final java.util.List<java.util.List<String>> wrappedHeaders = new java.util.ArrayList<>();
        wrappedHeaders.add(wrapText("Station", headerFont, headerFontSize, colWidths[0] - 2 * cellMargin));
        wrappedHeaders.add(wrapText("EVSE", headerFont, headerFontSize, colWidths[1] - 2 * cellMargin));
        wrappedHeaders.add(wrapText("Conn.", headerFont, headerFontSize, colWidths[2] - 2 * cellMargin));
        wrappedHeaders.add(wrapText("User", headerFont, headerFontSize, colWidths[3] - 2 * cellMargin));
        wrappedHeaders.add(wrapText("Start", headerFont, headerFontSize, colWidths[4] - 2 * cellMargin));
        wrappedHeaders.add(wrapText("End", headerFont, headerFontSize, colWidths[5] - 2 * cellMargin));
        wrappedHeaders.add(wrapText("Energy (kWh)", headerFont, headerFontSize, colWidths[6] - 2 * cellMargin));
        wrappedHeaders.add(wrapText("Status", headerFont, headerFontSize, colWidths[7] - 2 * cellMargin));

        int maxLines = 1;
        for (final java.util.List<String> lines : wrappedHeaders) {
            maxLines = Math.max(maxLines, lines.size());
        }
        final float dynamicHeaderHeight = Math.max(rowHeight, maxLines * (headerFontSize + 2) + 10);

        // Horizontal line top
        cs.moveTo(margin, y);
        cs.lineTo(margin + tableWidth, y);
        cs.stroke();

        drawRow(cs, margin, y, colWidths, dynamicHeaderHeight, cellMargin, headerFontSize, wrappedHeaders);

        // Horizontal line bottom
        cs.moveTo(margin, y - dynamicHeaderHeight);
        cs.lineTo(margin + tableWidth, y - dynamicHeaderHeight);
        cs.stroke();
        
        return dynamicHeaderHeight;
    }

    private java.util.List<String> wrapText(final String text, final org.apache.pdfbox.pdmodel.font.PDFont font, 
                                          final float fontSize, final float maxWidth) throws IOException {
        final List<String> lines = new ArrayList<>();
        if (text == null || text.isEmpty()) {
            lines.add("");
            return lines;
        }

        final String[] words = text.split("\\s+");
        StringBuilder currentLine = new StringBuilder();
        
        for (final String word : words) {
            final String testLine = currentLine.isEmpty() ? word : currentLine + " " + word;
            final float width = font.getStringWidth(testLine) / 1000 * fontSize;
            if (width > maxWidth && !currentLine.isEmpty()) {
                lines.add(currentLine.toString());
                currentLine = new StringBuilder(word);
            } else {
                currentLine = new StringBuilder(testLine);
            }
        }
        
        if (!currentLine.isEmpty()) {
            lines.add(currentLine.toString());
        }
        
        return lines;
    }

    private void drawRow(final PDPageContentStream cs, final float margin, final float y, 
                        final float[] colWidths, final float totalRowHeight, final float cellMargin,
                        final float fontSize, final java.util.List<java.util.List<String>> cellLines) throws IOException {
        
        float currentX = margin;
        for (int i = 0; i < cellLines.size(); i++) {
            final java.util.List<String> lines = cellLines.get(i);
            float lineY = y - 15; // Start position for first line
            
            for (final String line : lines) {
                cs.beginText();
                cs.newLineAtOffset(currentX + cellMargin, lineY);
                cs.showText(line);
                cs.endText();
                lineY -= (fontSize + 2); // Small gap between lines
            }
            
            // Vertical line
            cs.moveTo(currentX, y);
            cs.lineTo(currentX, y - totalRowHeight);
            cs.stroke();
            
            currentX += colWidths[i];
        }
        // Last vertical line
        cs.moveTo(currentX, y);
        cs.lineTo(currentX, y - totalRowHeight);
        cs.stroke();
    }

    private void drawRow(final PDPageContentStream cs, final float margin, final float y, 
                        final float colWidth, final float rowHeight, final float cellMargin,
                        final String... cells) throws IOException {
        final List<List<String>> wrappedCells = new ArrayList<>();
        for (final String cell : cells) {
            wrappedCells.add(Collections.singletonList(cell != null ? cell : ""));
        }
        final float[] colWidths = new float[cells.length];
        Arrays.fill(colWidths, colWidth);
        drawRow(cs, margin, y, colWidths, rowHeight, cellMargin, 10, wrappedCells);
    }
}