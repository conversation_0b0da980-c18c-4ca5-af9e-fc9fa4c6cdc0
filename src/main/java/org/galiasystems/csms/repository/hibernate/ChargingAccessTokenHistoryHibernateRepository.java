package org.galiasystems.csms.repository.hibernate;

import java.util.Collections;
import java.util.List;

import org.galiasystems.csms.management.filter.QueryFilter;
import org.galiasystems.csms.management.model.ChargingAccessTokenHistory;
import org.galiasystems.csms.management.repository.ChargingAccessTokenHistoryRepository;
import org.galiasystems.csms.repository.hibernate.criteriaBuilder.CriteriaQueryHelper;
import org.galiasystems.csms.repository.hibernate.model.ChargingAccessTokenHistoryEntity;
import org.galiasystems.csms.repository.hibernate.utils.HibernateRepositoryEntityMapper;

import io.quarkus.hibernate.reactive.panache.PanacheRepository;
import io.quarkus.hibernate.reactive.panache.common.WithSession;
import io.quarkus.hibernate.reactive.panache.common.WithTransaction;
import io.smallrye.mutiny.Multi;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;

@ApplicationScoped
public class ChargingAccessTokenHistoryHibernateRepository implements ChargingAccessTokenHistoryRepository, 
                PanacheRepository<ChargingAccessTokenHistoryEntity> {

    @Inject
    private HibernateRepositoryEntityMapper entityMapper;
    
    @Inject
    private CriteriaQueryHelper criteriaQueryHelper;
    
    
    @Override
    @WithTransaction
    public Uni<ChargingAccessTokenHistory> createChargingAccessTokenHistory(
            final ChargingAccessTokenHistory chargingAccessTokenHistory) {
        
        final ChargingAccessTokenHistoryEntity chargingAccessTokenHistoryEntity = 
                this.entityMapper.createChargingAccessTokenHistoryEntity(chargingAccessTokenHistory);
        return persist(chargingAccessTokenHistoryEntity)
                .map(this.entityMapper::createChargingAccessTokenHistory);
    }

    @Override
    @WithSession
    public Uni<List<ChargingAccessTokenHistory>> listChargingAccessTokenHistories(final QueryFilter queryFilter) {
        
        return criteriaQueryHelper.list(
                ChargingAccessTokenHistoryEntity.class,
                null,
                Collections.emptySet(),
                queryFilter,
                this.entityMapper::createChargingAccessTokenHistoryList
        );
    }

    @Override
    public Multi<ChargingAccessTokenHistory> streamChargingAccessTokenHistories(final QueryFilter queryFilter) {
     
        return criteriaQueryHelper.stream(
                ChargingAccessTokenHistoryEntity.class,
                null,
                Collections.emptySet(),
                queryFilter,
                this.entityMapper::createChargingAccessTokenHistory
        );
    }
}
