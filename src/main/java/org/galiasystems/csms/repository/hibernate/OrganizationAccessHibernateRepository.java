package org.galiasystems.csms.repository.hibernate;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.function.Consumer;

import org.galiasystems.csms.management.filter.QueryFilter;
import org.galiasystems.csms.management.model.OrganizationAccess;
import org.galiasystems.csms.management.repository.OrganizationAccessRepository;
import org.galiasystems.csms.repository.hibernate.criteriaBuilder.CriteriaQueryHelper;
import org.galiasystems.csms.repository.hibernate.model.OrganizationAccessEntity;
import org.galiasystems.csms.repository.hibernate.utils.HibernateRepositoryEntityMapper;

import io.quarkus.hibernate.reactive.panache.PanacheRepository;
import io.quarkus.hibernate.reactive.panache.common.WithSession;
import io.quarkus.hibernate.reactive.panache.common.WithTransaction;
import io.smallrye.mutiny.Multi;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.NoResultException;

@ApplicationScoped
public class OrganizationAccessHibernateRepository implements OrganizationAccessRepository, 
                PanacheRepository<OrganizationAccessEntity> {
    
    @Inject
    private HibernateRepositoryEntityMapper entityMapper;
    
    @Inject
    private CriteriaQueryHelper criteriaQueryHelper;

    @Override
    @WithTransaction
    public Uni<OrganizationAccess> createOrganizationAccess(final OrganizationAccess organizationAccess) {
        
        final OrganizationAccessEntity organizationAccessEntity = 
                this.entityMapper.createOrganizationAccessEntity(organizationAccess);
        return persist(organizationAccessEntity)
                .map(this.entityMapper::createOrganizationAccess);
    }

    @Override
    @WithSession
    public Uni<Optional<OrganizationAccess>> getOrganizationAccess(final Long id) {
        
        return findById(id)
                .map(this.entityMapper::createOrganizationAccess)
                .map(Optional::ofNullable);
    }

    @Override
    @WithTransaction
    public Uni<OrganizationAccess> updateOrganizationAccess(final Long id, 
            final Consumer<OrganizationAccess> changeCallback) {
        
        return findById(id)
                .onItem()
                .ifNull()
                .failWith(new NoResultException("Organization access with id = " + id + " not found for update."))
                .map((organizationAccessEntity) -> 
                    this.updateOrganizationAccessEntity(organizationAccessEntity, changeCallback));
    }
    
    private OrganizationAccess updateOrganizationAccessEntity(final OrganizationAccessEntity organizationAccessEntity, 
            final Consumer<OrganizationAccess> changeCallback) {
        
        final OrganizationAccess organizationAccess = this.entityMapper.createOrganizationAccess(organizationAccessEntity);
        changeCallback.accept(organizationAccess);
        this.entityMapper.updateOrganizationAccessEntity(organizationAccessEntity, organizationAccess);
        return this.entityMapper.createOrganizationAccess(organizationAccessEntity);
    }
    
    @Override
    @WithTransaction
    public Uni<Boolean> removeOrganizationAccess(final Long id) {
        
        return deleteById(id);
    }

    @Override
    @WithSession
    public Uni<Optional<OrganizationAccess>> findOrganizationAccess(final QueryFilter queryFilter) {
        
        return criteriaQueryHelper.find(
                OrganizationAccessEntity.class,
                null,
                Collections.emptySet(),
                queryFilter,
                this.entityMapper::createOrganizationAccess
        );
    }

    @Override
    @WithSession
    public Uni<List<OrganizationAccess>> listOrganizationAccesses(final QueryFilter queryFilter) {
        
        return criteriaQueryHelper.list(
                OrganizationAccessEntity.class,
                null,
                Collections.emptySet(),
                queryFilter,
                this.entityMapper::createOrganizationAccessList
        );
    }
    
    @Override
    public Multi<OrganizationAccess> streamOrganizationAccesses(final QueryFilter queryFilter) {
        
        return criteriaQueryHelper.stream(
                OrganizationAccessEntity.class,
                null,
                Collections.emptySet(),
                queryFilter,
                this.entityMapper::createOrganizationAccess
        );
    }
}
