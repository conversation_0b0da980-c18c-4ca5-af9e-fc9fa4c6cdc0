package org.galiasystems.csms.repository.hibernate;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.function.Consumer;

import org.galiasystems.csms.management.filter.QueryFilter;
import org.galiasystems.csms.management.model.ChargingStationAccess;
import org.galiasystems.csms.management.repository.ChargingStationAccessRepository;
import org.galiasystems.csms.repository.hibernate.criteriaBuilder.CriteriaQueryHelper;
import org.galiasystems.csms.repository.hibernate.model.ChargingStationAccessEntity;
import org.galiasystems.csms.repository.hibernate.utils.HibernateRepositoryEntityMapper;

import io.quarkus.hibernate.reactive.panache.PanacheRepository;
import io.quarkus.hibernate.reactive.panache.common.WithSession;
import io.quarkus.hibernate.reactive.panache.common.WithTransaction;
import io.smallrye.mutiny.Multi;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.NoResultException;

@ApplicationScoped
public class ChargingStationAccessHibernateRepository implements ChargingStationAccessRepository, 
                PanacheRepository<ChargingStationAccessEntity> {
    
    @Inject
    private HibernateRepositoryEntityMapper entityMapper;
    
    @Inject
    private CriteriaQueryHelper criteriaQueryHelper;

    @Override
    @WithTransaction
    public Uni<ChargingStationAccess> createChargingStationAccess(final ChargingStationAccess chargingStationAccess) {
        
        final ChargingStationAccessEntity chargingStationAccessEntity = 
                this.entityMapper.createChargingStationAccessEntity(chargingStationAccess);
        return persist(chargingStationAccessEntity)
                .map(this.entityMapper::createChargingStationAccess);
    }

    @Override
    @WithSession
    public Uni<Optional<ChargingStationAccess>> getChargingStationAccess(final Long id) {
        
        return findById(id)
                .map(this.entityMapper::createChargingStationAccess)
                .map(Optional::ofNullable);
    }

    @Override
    @WithTransaction
    public Uni<ChargingStationAccess> updateChargingStationAccess(final Long id,
            final Consumer<ChargingStationAccess> changeCallback) {
        
        return findById(id)
                .onItem()
                .ifNull()
                .failWith(new NoResultException("ChargingStation access with id = " + id + " not found for update."))
                .map((chargingStationAccess) -> 
                    this.updateChargingStationAccessEntity(chargingStationAccess, changeCallback));
    }
    
    private ChargingStationAccess updateChargingStationAccessEntity(final ChargingStationAccessEntity chargingStationAccessEntity, 
            final Consumer<ChargingStationAccess> changeCallback) {
        
        final ChargingStationAccess chargingStationAccess = this.entityMapper.createChargingStationAccess(chargingStationAccessEntity);
        changeCallback.accept(chargingStationAccess);
        this.entityMapper.updateChargingStationAccessEntity(chargingStationAccessEntity, chargingStationAccess);
        return this.entityMapper.createChargingStationAccess(chargingStationAccessEntity);
    }
    
    @Override
    @WithTransaction
    public Uni<Boolean> removeChargingStationAccess(final Long id) {
        
        return deleteById(id);
    }

    
    @Override
    @WithSession
    public Uni<Boolean> hasChargingStationAccess(final QueryFilter queryFilter) {
        
        return criteriaQueryHelper.hasResult(
                ChargingStationAccessEntity.class,
                Collections.emptySet(),
                queryFilter
        );
    }
    
    @Override
    @WithSession
    public Uni<Optional<ChargingStationAccess>> findChargingStationAccess(final QueryFilter queryFilter) {
        
        return criteriaQueryHelper.find(
                ChargingStationAccessEntity.class,
                null,
                Collections.emptySet(),
                queryFilter,
                this.entityMapper::createChargingStationAccess
        );
    }

    @Override
    @WithSession
    public Uni<List<ChargingStationAccess>> listChargingStationAccesses(final QueryFilter queryFilter) {
        
        return criteriaQueryHelper.list(
                ChargingStationAccessEntity.class,
                null,
                Collections.emptySet(),
                queryFilter,
                this.entityMapper::createChargingStationAccessList
        );
    }
    
    @Override
    public Multi<ChargingStationAccess> streamChargingStationAccesses(final QueryFilter queryFilter) {
        
        return criteriaQueryHelper.stream(
                ChargingStationAccessEntity.class,
                null,
                Collections.emptySet(),
                queryFilter,
                this.entityMapper::createChargingStationAccess
        );
    }
}
