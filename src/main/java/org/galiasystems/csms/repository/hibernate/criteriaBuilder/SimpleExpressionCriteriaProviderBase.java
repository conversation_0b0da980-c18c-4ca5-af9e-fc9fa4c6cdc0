package org.galiasystems.csms.repository.hibernate.criteriaBuilder;

import java.util.Collection;
import java.util.List;
import java.util.function.BiFunction;

import jakarta.persistence.criteria.Nulls;
import org.galiasystems.csms.management.filter.QueryFilterOrderNullPrecedence;
import org.galiasystems.csms.management.filter.QueryFilterOrderSortDirection;
import org.galiasystems.csms.management.model.enums.attribute.BusinessEntityAttribute;
import org.hibernate.query.criteria.HibernateCriteriaBuilder;
import org.hibernate.query.criteria.JpaOrder;

import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.Expression;
import jakarta.persistence.criteria.Predicate;

/**
 * Interface for creating expression for the given entity type with the given type.
 * 
 * @param <T> the type of the root entity.
 * @param <A> the type referenced by the expression.
 */
public abstract class SimpleExpressionCriteriaProviderBase<T, A> 
                implements BusinessAttributeCriteriaProvider<T, A> {

    private final BusinessEntityAttribute businessAttribute;
    
    public SimpleExpressionCriteriaProviderBase(final BusinessEntityAttribute businessAttribute) {
        this.businessAttribute = businessAttribute;
    }
    
    @Override
	public List<Predicate> equal(final CriteriaContext<T> context, final Object value) {
	    return this.createSingleExpressionPredicate(context, 
	            (cb, expression) -> cb.equal(expression, value));
	}
    
    @Override
    public List<Predicate> isNull(final CriteriaContext<T> context) {
        return this.createSingleExpressionPredicate(context, 
                (cb, expression) -> cb.isNull(expression));
    }
    
    @Override
    public List<Predicate> isNotNull(final CriteriaContext<T> context) {
        return this.createSingleExpressionPredicate(context, 
                (cb, expression) -> cb.isNotNull(expression));
    }

    @Override
	public List<Predicate> in(final CriteriaContext<T> context, final Collection<?> values) {
        return this.createSingleExpressionPredicate(context, 
                (cb, expression) -> cb.and(expression.in(values)));
    }
    
    @Override
    @SuppressWarnings({ "unchecked", "rawtypes" })
    public List<Predicate> greaterThan(final CriteriaContext<T> context, final Comparable<?> valueFrom) {

        return this.createSingleExpressionPredicate(context, 
                (cb, expression) -> cb.greaterThan((Expression)expression, (Comparable)valueFrom));
    }
	
    @Override
    @SuppressWarnings({ "unchecked", "rawtypes" })
    public List<Predicate> greaterThanOrEqualTo(final CriteriaContext<T> context, final Comparable<?> valueFrom) {

        return this.createSingleExpressionPredicate(context, 
                (cb, expression) -> cb.greaterThanOrEqualTo((Expression)expression, (Comparable)valueFrom));
    }
    
    @Override
    @SuppressWarnings({ "unchecked", "rawtypes" })
    public List<Predicate> lessThan(final CriteriaContext<T> context, final Comparable<?> valueTo) {

        return this.createSingleExpressionPredicate(context, 
                (cb, expression) -> cb.lessThan((Expression)expression, (Comparable)valueTo));
    }
	
    @Override
    @SuppressWarnings({ "unchecked", "rawtypes" })
    public List<Predicate> lessThanOrEqualTo(final CriteriaContext<T> context, final Comparable<?> valueTo) {

        return this.createSingleExpressionPredicate(context, 
                (cb, expression) -> cb.lessThanOrEqualTo((Expression)expression, (Comparable)valueTo));
    }
    
    private List<Predicate> createSingleExpressionPredicate(final CriteriaContext<T> context,
	        final BiFunction<CriteriaBuilder, Expression<A>, Predicate> predicateBuilder) {
	    
	    final CriteriaBuilder cb = context.getCriteriaBuilder();
        final Expression<A> expression = this.getExpression(context);
        final Predicate predicate = predicateBuilder.apply(cb, expression);
        return List.of(predicate);
	}
	
	@Override
	public JpaOrder order(final CriteriaContext<T> context, 
	        final QueryFilterOrderSortDirection sortDirection, final QueryFilterOrderNullPrecedence nullOrder) {
	    
	    final CriteriaBuilder cb = context.getCriteriaBuilder();
	    final HibernateCriteriaBuilder hcb = (HibernateCriteriaBuilder) cb;
	    final Expression<A> expression = this.getExpression(context);
	    
	    final JpaOrder order;
        switch(sortDirection) {
            case ASC:
                order = hcb.asc(expression);
                break;
            case DESC:
                order = hcb.desc(expression);
                break;
            default: 
                throw new IllegalStateException("Unknown order! order: " + sortDirection.name());
        }
        
        switch(nullOrder) {
            case FIRST:
                return order.nullPrecedence(Nulls.FIRST);
            case LAST:
                return order.nullPrecedence(Nulls.LAST);
            default: 
                throw new IllegalStateException("Unknown null order precedence! precedence: " + nullOrder.name());
        }
	}
	
	private Expression<A> getExpression(final CriteriaContext<T> context) {
        return context.getAttributeExpression(this.businessAttribute, this);
    }
}