package org.galiasystems.csms.repository.hibernate;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.function.Consumer;

import org.galiasystems.csms.management.filter.QueryFilter;
import org.galiasystems.csms.management.model.User;
import org.galiasystems.csms.management.model.enums.OrganizationAccessRole;
import org.galiasystems.csms.management.model.enums.attribute.UserAttribute;
import org.galiasystems.csms.management.repository.UserRepository;
import org.galiasystems.csms.repository.hibernate.base.CsmsUserHibernateRepositoryBase;
import org.galiasystems.csms.repository.hibernate.criteriaBuilder.CriteriaQueryHelper;
import org.galiasystems.csms.repository.hibernate.model.CsmsUserEntity;
import org.galiasystems.csms.repository.hibernate.utils.HibernateRepositoryEntityMapper;

import io.quarkus.hibernate.reactive.panache.common.WithSession;
import io.quarkus.hibernate.reactive.panache.common.WithTransaction;
import io.smallrye.mutiny.Multi;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.NoResultException;

@ApplicationScoped
public class CsmsUserHibernateRepository extends CsmsUserHibernateRepositoryBase 
                implements UserRepository {

    private static final List<OrganizationAccessRole> DEFAULT_ORGANIZATION_ACCESS_ROLE_FILTER_VALUES = 
            List.of(OrganizationAccessRole.Owner, OrganizationAccessRole.Admin);
    
    @Inject
    private HibernateRepositoryEntityMapper entityMapper;
    
    @Inject
    private CriteriaQueryHelper criteriaQueryHelper;


    @Override
    @WithTransaction
    public Uni<User> createUser(final User user) {
        
        final CsmsUserEntity csmsUserEntity = this.createNewCsmsUserEntity(user);
        return persist(csmsUserEntity)
                .map(this.entityMapper::createUser);
    }
    
    @Override
    @WithSession
    public Uni<Optional<User>> getUser(final Long id) {

        return findUserEntityById(id)
                .map(this.entityMapper::createUser)
                .map(Optional::ofNullable);
    }
    
    @Override
    @WithTransaction
    public Uni<User> updateUser(final Long id, final Consumer<User> changeCallback) {
        
        return findUserEntityById(id)
                .onItem()
                .ifNull()
                .failWith(new NoResultException("User with id = " + id + " not found for update."))
                .map((csmsUserEntity) -> 
                    this.updateUserEntity(csmsUserEntity, changeCallback));
    }
    
    @Override
    @WithTransaction
    public Uni<User> updateUser(final QueryFilter queryFilter, final Consumer<User> changeCallback) {
        
        return this.criteriaQueryHelper.findNullable(CsmsUserEntity.class, null, Collections.emptySet(), 
                        this.addDefaultFiltering(queryFilter))
                .map((csmsUserEntity) -> 
                    this.updateUserEntity(csmsUserEntity, changeCallback));
    }
    
    private Uni<CsmsUserEntity> findUserEntityById(final Long id) {
        
        final QueryFilter idQueryFilter = new QueryFilter()
                .addEqualFilter(UserAttribute.ID, id);
        
        final QueryFilter queryFilter;
        if (id.equals(this.getUserId())) {
            queryFilter = idQueryFilter;
        } else {
            queryFilter = this.addDefaultFiltering(idQueryFilter);
        }
        
        return criteriaQueryHelper.findNullable(
                CsmsUserEntity.class,
                null,
                Collections.emptySet(),
                queryFilter
        );
    }

    @Override
    @WithSession
    public Uni<Optional<User>> findUser(final QueryFilter queryFilter) {
        
        return criteriaQueryHelper.find(
                CsmsUserEntity.class,
                null,
                Collections.emptySet(),
                this.addDefaultFiltering(queryFilter),
                this.entityMapper::createUser
        );
    }

    @Override
    @WithSession
    public Uni<List<User>> listUsers(final QueryFilter queryFilter) {
        
        return criteriaQueryHelper.list(
                CsmsUserEntity.class,
                null,
                Collections.emptySet(),
                this.addDefaultFiltering(queryFilter),
                this.entityMapper::createUserList
        );
    }
    
    @Override
    public Multi<User> streamUsers(final QueryFilter queryFilter) {
        
        return criteriaQueryHelper.stream(
                CsmsUserEntity.class,
                null,
                Collections.emptySet(),
                this.addDefaultFiltering(queryFilter),
                this.entityMapper::createUser
        );
    }
    
    private QueryFilter addDefaultFiltering(final QueryFilter queryFilter) {
        return queryFilter
            .addEqualFilter(UserAttribute.ORGANIZATION_ACCESS_USER_ID, this.getUserId())
            .addInFilter(UserAttribute.ORGANIZATION_ACCESS_ROLE, DEFAULT_ORGANIZATION_ACCESS_ROLE_FILTER_VALUES);
    }
}