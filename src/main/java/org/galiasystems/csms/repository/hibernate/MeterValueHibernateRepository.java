package org.galiasystems.csms.repository.hibernate;

import io.quarkus.hibernate.reactive.panache.PanacheRepository;
import io.quarkus.hibernate.reactive.panache.common.WithSession;
import io.quarkus.hibernate.reactive.panache.common.WithTransaction;
import io.smallrye.mutiny.Multi;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;

import org.galiasystems.csms.management.filter.QueryFilter;
import org.galiasystems.csms.management.model.MeterValue;
import org.galiasystems.csms.management.model.enums.attribute.MeterValueAttribute;
import org.galiasystems.csms.management.repository.MeterValueRepository;
import org.galiasystems.csms.repository.hibernate.criteriaBuilder.CriteriaQueryHelper;
import org.galiasystems.csms.repository.hibernate.model.MeterValueEntity;
import org.galiasystems.csms.repository.hibernate.utils.HibernateRepositoryEntityMapper;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

@ApplicationScoped
public class MeterValueHibernateRepository extends HibernateRepositoryBase implements MeterValueRepository, 
                PanacheRepository<MeterValueEntity> {
    
    @Inject
    private HibernateRepositoryEntityMapper entityMapper;


    @Inject
    private CriteriaQueryHelper criteriaQueryHelper;
    
    @Override
    @WithTransaction
    public Uni<Void> createMeterValues(final Long chargingStationId, final Integer chargingStationEvseId, 
			final Collection<MeterValue> meterValues) {
       
        List<MeterValueEntity> meterValuesToPersist = new ArrayList<>();
        meterValues.forEach(meterValue -> meterValuesToPersist.addAll(
        		this.entityMapper.createMeterValueEntities(chargingStationId, chargingStationEvseId, meterValue)));
        return persist(meterValuesToPersist);
    }
    
    @Override
    @WithSession
    public Uni<List<MeterValue>> listMeterValues(final QueryFilter queryFilter) {
        
        return criteriaQueryHelper.list(
                MeterValueEntity.class,
                null,
                Collections.emptySet(),
                this.addDefaultFiltering(queryFilter),
                this.entityMapper::createMeterValueList
        );
    }
    
    @Override
    public Multi<MeterValue> streamMeterValues(final QueryFilter queryFilter) {
        
        return criteriaQueryHelper.stream(
                MeterValueEntity.class,
                null,
                Collections.emptySet(),
                this.addDefaultFiltering(queryFilter),
                this.entityMapper::createMeterValue
        );
    }

    private QueryFilter addDefaultFiltering(final QueryFilter queryFilter) {
        return queryFilter
            .addEqualFilter(MeterValueAttribute.CHARGING_STATION_ACCESS_USER_ID, this.getUserId());
    }
}