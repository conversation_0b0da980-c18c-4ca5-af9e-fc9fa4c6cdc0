package org.galiasystems.csms.repository.hibernate;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.function.Consumer;

import org.galiasystems.csms.management.filter.QueryFilter;
import org.galiasystems.csms.management.model.Location;
import org.galiasystems.csms.management.model.enums.attribute.LocationAttribute;
import org.galiasystems.csms.management.repository.LocationRepository;
import org.galiasystems.csms.repository.hibernate.criteriaBuilder.CriteriaQueryHelper;
import org.galiasystems.csms.repository.hibernate.model.LocationEntity;
import org.galiasystems.csms.repository.hibernate.utils.HibernateRepositoryEntityMapper;

import io.quarkus.hibernate.reactive.panache.PanacheRepository;
import io.quarkus.hibernate.reactive.panache.common.WithSession;
import io.quarkus.hibernate.reactive.panache.common.WithTransaction;
import io.smallrye.mutiny.Multi;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.NoResultException;

@ApplicationScoped
public class LocationHibernateRepository extends HibernateRepositoryBase implements LocationRepository, 
                PanacheRepository<LocationEntity> {

    @Inject
    private HibernateRepositoryEntityMapper entityMapper;
    
    @Inject
    private CriteriaQueryHelper criteriaQueryHelper;

    @Override
    @WithTransaction
    public Uni<Location> createLocation(final Location location) {
        
        final LocationEntity locationEntity = this.entityMapper.createLocationEntity(location);
        return persist(locationEntity)
                .map(this.entityMapper::createLocation);
    }

    @Override
    @WithSession
    public Uni<Optional<Location>> getLocation(final Long id) {
        
        return findLocationEntityById(id)
                .map(this.entityMapper::createLocation)
                .map(Optional::ofNullable);
    }

    @Override
    @WithTransaction
    public Uni<Location> updateLocation(final Long id, final Consumer<Location> changeCallback) {
        
        return findLocationEntityById(id)
                .onItem()
                .ifNull()
                .failWith(new NoResultException("Location with id = " + id + " not found for update."))
                .map((locationEntity) -> 
                    this.updateLocationEntity(locationEntity, changeCallback));
    }
    
    private Location updateLocationEntity(final LocationEntity locationEntity, 
            final Consumer<Location> changeCallback) {
        
        final Location location = this.entityMapper.createLocation(locationEntity);
        changeCallback.accept(location);
        this.entityMapper.updateLocationEntity(locationEntity, location);
        return this.entityMapper.createLocation(locationEntity);
    }
    
    private Uni<LocationEntity> findLocationEntityById(final Long id) {
        
        final QueryFilter queryFilter = new QueryFilter()
                .addEqualFilter(LocationAttribute.ID, id);
        
        return criteriaQueryHelper.findNullable(
                LocationEntity.class,
                null,
                Collections.emptySet(),
                this.addDefaultFiltering(queryFilter)
        );
    }

    @Override
    @WithSession
    public Uni<Optional<Location>> findLocation(final QueryFilter queryFilter) {
        
        return criteriaQueryHelper.find(
                LocationEntity.class,
                null,
                Collections.emptySet(),
                this.addDefaultFiltering(queryFilter),
                this.entityMapper::createLocation
        );
    }

    @Override
    @WithSession
    public Uni<List<Location>> listLocations(final QueryFilter queryFilter) {
        
        return criteriaQueryHelper.list(
                LocationEntity.class,
                null,
                Collections.emptySet(),
                this.addDefaultFiltering(queryFilter),
                this.entityMapper::createLocationList
        );
    }

    @Override
    public Multi<Location> streamLocations(final QueryFilter queryFilter) {
        
        return criteriaQueryHelper.stream(
                LocationEntity.class,
                null,
                Collections.emptySet(),
                this.addDefaultFiltering(queryFilter),
                this.entityMapper::createLocation
        );
    }

    private QueryFilter addDefaultFiltering(final QueryFilter queryFilter) {
        return queryFilter
            .addEqualFilter(LocationAttribute.LOCATION_ACCESS_USER_ID, this.getUserId());
    }
}