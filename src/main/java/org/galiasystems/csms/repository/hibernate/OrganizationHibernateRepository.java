package org.galiasystems.csms.repository.hibernate;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.function.Consumer;

import org.galiasystems.csms.management.filter.QueryFilter;
import org.galiasystems.csms.management.model.Organization;
import org.galiasystems.csms.management.model.enums.attribute.OrganizationAttribute;
import org.galiasystems.csms.management.repository.OrganizationRepository;
import org.galiasystems.csms.repository.hibernate.criteriaBuilder.CriteriaQueryHelper;
import org.galiasystems.csms.repository.hibernate.model.OrganizationEntity;
import org.galiasystems.csms.repository.hibernate.utils.HibernateRepositoryEntityMapper;

import io.quarkus.hibernate.reactive.panache.PanacheRepository;
import io.quarkus.hibernate.reactive.panache.common.WithSession;
import io.quarkus.hibernate.reactive.panache.common.WithTransaction;
import io.smallrye.mutiny.Multi;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.NoResultException;

@ApplicationScoped
public class OrganizationHibernateRepository extends HibernateRepositoryBase implements OrganizationRepository, 
                PanacheRepository<OrganizationEntity> {

    @Inject
    private HibernateRepositoryEntityMapper entityMapper;
    
    @Inject
    private CriteriaQueryHelper criteriaQueryHelper;
    
    @Override
    @WithTransaction
    public Uni<Organization> createOrganization(final Organization organization) {
       
        final OrganizationEntity organizationEntity = this.entityMapper.createOrganizationEntity(organization);
        return persist(organizationEntity)
                .map(this.entityMapper::createOrganization);
    }

    @Override
    @WithSession
    public Uni<Optional<Organization>> getOrganization(final Long id) {
        
        return findOrganizationEntityById(id)
                .map(this.entityMapper::createOrganization)
                .map(Optional::ofNullable);
    }

    @Override
    @WithTransaction
    public Uni<Organization> updateOrganization(final Long id, final Consumer<Organization> changeCallback) {
        
        return findOrganizationEntityById(id)
                .onItem()
                .ifNull()
                .failWith(new NoResultException("Organization with id = " + id + " not found for update."))
                .map((organizationEntity) -> 
                    this.updateOrganizationEntity(organizationEntity, changeCallback));
    }
    
    private Organization updateOrganizationEntity(final OrganizationEntity organizationEntity, 
            final Consumer<Organization> changeCallback) {
        
        final Organization organization = this.entityMapper.createOrganization(organizationEntity);
        changeCallback.accept(organization);
        this.entityMapper.updateOrganizationEntity(organizationEntity, organization);
        return this.entityMapper.createOrganization(organizationEntity);
    }
    
    private Uni<OrganizationEntity> findOrganizationEntityById(final Long id) {
        
        final QueryFilter queryFilter = new QueryFilter()
                .addEqualFilter(OrganizationAttribute.ID, id);
        
        return criteriaQueryHelper.findNullable(
                OrganizationEntity.class,
                null,
                Collections.emptySet(),
                this.addDefaultFiltering(queryFilter)
        );
    }

    @Override
    @WithSession
    public Uni<Optional<Organization>> findOrganization(final QueryFilter queryFilter) {
        
        return criteriaQueryHelper.find(
                OrganizationEntity.class,
                null,
                Collections.emptySet(),
                this.addDefaultFiltering(queryFilter),
                this.entityMapper::createOrganization
        );
    }

    @Override
    @WithSession
    public Uni<List<Organization>> listOrganizations(final QueryFilter queryFilter) {
        
        return criteriaQueryHelper.list(
                OrganizationEntity.class,
                null,
                Collections.emptySet(),
                this.addDefaultFiltering(queryFilter),
                this.entityMapper::createOrganizationList
        );
    }

    @Override
    public Multi<Organization> streamOrganizations(final QueryFilter queryFilter) {
        
        return criteriaQueryHelper.stream(
                OrganizationEntity.class,
                null,
                Collections.emptySet(),
                this.addDefaultFiltering(queryFilter),
                this.entityMapper::createOrganization
        );
    }

    private QueryFilter addDefaultFiltering(final QueryFilter queryFilter) {
        return queryFilter
            .addEqualFilter(OrganizationAttribute.ORGANIZATION_ACCESS_USER_ID, this.getUserId());
    }
}
