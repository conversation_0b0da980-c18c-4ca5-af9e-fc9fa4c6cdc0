package org.galiasystems.csms.repository.hibernate;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import org.galiasystems.csms.management.filter.QueryFilter;
import org.galiasystems.csms.management.model.ChargingStationVariable;
import org.galiasystems.csms.management.repository.ChargingStationVariableRepository;
import org.galiasystems.csms.repository.hibernate.criteriaBuilder.CriteriaQueryHelper;
import org.galiasystems.csms.repository.hibernate.model.ChargingStationEntity;
import org.galiasystems.csms.repository.hibernate.model.ChargingStationVariableEntity;
import org.galiasystems.csms.repository.hibernate.utils.HibernateRepositoryEntityMapper;
import org.galiasystems.csms.utils.Pair;
import org.hibernate.reactive.mutiny.Mutiny;

import io.quarkus.hibernate.reactive.panache.PanacheRepository;
import io.quarkus.hibernate.reactive.panache.common.WithSession;
import io.quarkus.hibernate.reactive.panache.common.WithTransaction;
import io.smallrye.mutiny.Multi;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.NoResultException;

@ApplicationScoped
public class ChargingStationVariableHibernateRepository implements ChargingStationVariableRepository, 
				PanacheRepository<ChargingStationVariableEntity> {

	private final HibernateRepositoryEntityMapper entityMapper;
	
	private final CriteriaQueryHelper criteriaQueryHelper;
	
	private final ChargingStationVariableValueHibernateRepository chargingStationVariableValueRepository;

	@Inject
	public ChargingStationVariableHibernateRepository(final HibernateRepositoryEntityMapper entityMapper,
	        final CriteriaQueryHelper criteriaQueryHelper, 
	        final ChargingStationVariableValueHibernateRepository chargingStationVariableValueRepository) {
	    
		this.entityMapper = entityMapper;
		this.criteriaQueryHelper = criteriaQueryHelper;
		this.chargingStationVariableValueRepository = chargingStationVariableValueRepository;
	}
	
	@Override
	@WithSession
	public Uni<List<ChargingStationVariable>> listChargingStationVariables(final QueryFilter queryFilter) {
		
	    return criteriaQueryHelper.list(
	            ChargingStationVariableEntity.class,
                null,
                Collections.emptySet(),
                queryFilter,
                this.entityMapper::createChargingStationVariableList
        );
	}
	
	@Override
	public Multi<ChargingStationVariable> streamChargingStationVariables(final QueryFilter queryFilter) {
	    
	    return criteriaQueryHelper.stream(
	            ChargingStationVariableEntity.class,
                null,
                Collections.emptySet(),
                queryFilter,
                this.entityMapper::createChargingStationVariable
        );
	}
	
	@Override
    @WithTransaction
    public Uni<List<ChargingStationVariable>> upsertChargingStationVariables(final Long chargingStationId,
            final Collection<ChargingStationVariable> chargingStationVariables) {
        
        
                
//      final List<Uni<ChargingStationVariableEntity>> chargingStationVariableEntities = 
//      chargingStationVariables.stream().map((chargingStationVariable) -> 
//              this.getChargingStationVariableEntity(chargingStationId, chargingStationVariable.variableName())
//                  .call((hargingStationVariableEntity) -> Mutiny.fetch(hargingStationVariableEntity.getChargingStationVariableValues()))
//                  .onItem()
//                  .invoke((chargingStationVariableEntity) -> 
//                      this.updateChargingStationVariableEntity(chargingStationVariableEntity, chargingStationVariable)
//                  )
//                  .onFailure(NoResultException.class)
//                  .recoverWithUni(() -> this.createChargingStationVariableEntity(chargingStationEntity, chargingStationVariable))
//                  .call((chargingStationVariableEntity) -> 
//                      this.chargingStationVariableValueRepository
//                          .upsertChargingStationVariableValues(chargingStationVariableEntity.getId(), chargingStationVariable.chargingStationVariableValues()))
//          
//              ).toList();
//      
//      return Uni.join().all(chargingStationVariableEntities).usingConcurrencyOf(1).andFailFast()
//              .onItem()
//              .transform((chargingStationVariableEntityList) -> 
//                  chargingStationVariableEntityList.stream().map((chargingStationVariableEntity) ->
//                      this.entityMapper.createChargingStationVariable(chargingStationVariableEntity, 
//                              chargingStationVariableEntity.getChargingStationVariableValues()))
//                      .toList());
        
        
        return Multi.createFrom().items(chargingStationVariables.stream())
                .onItem()
                .transformToUni((chargingStationVariable) -> 
                    this.getChargingStationVariableEntity(chargingStationId, chargingStationVariable.variableName(),
                            chargingStationVariable.variableInstance())
                        .call((chargingStationVariableEntity) -> Mutiny.fetch(chargingStationVariableEntity.getChargingStationVariableValues()))
                        .invoke((chargingStationVariableEntity) -> 
                            this.updateChargingStationVariableEntity(chargingStationVariableEntity, 
                                    chargingStationVariable))
                        .onFailure(NoResultException.class)
                        .recoverWithUni(() -> {
                            final ChargingStationEntity chargingStationEntity = new ChargingStationEntity(chargingStationId);
                            return this.createChargingStationVariableEntity(chargingStationEntity, chargingStationVariable);
                        })
                        .call((chargingStationVariableEntity) -> 
                            this.chargingStationVariableValueRepository
                                    .upsertChargingStationVariableValues(chargingStationVariableEntity.getId(), chargingStationVariable.chargingStationVariableValues())
                        )
                        .map((chargingStationVariableEntity) -> 
                            this.entityMapper.createChargingStationVariable(chargingStationVariableEntity))
                ).merge(1)
                .collect()
                .asList();
    }
	
	public Uni<Void> mergeChargingStationVariables(
			final ChargingStationEntity chargingStationEntity,
			final Set<ChargingStationVariableEntity> chargingStationVariableEntities,
			final Collection<ChargingStationVariable> chargingStationVariables) {
		
		final Map<Pair<String, Optional<String>>, ChargingStationVariableEntity> existingVariables = new HashMap<>();
		
		chargingStationVariableEntities.forEach((chargingStationVariableEntity) -> 
			existingVariables.put(new Pair<>(chargingStationVariableEntity.getVariableName(),
					chargingStationVariableEntity.getVariableInstance()), chargingStationVariableEntity));
		
		final List<Uni<Void>> mergedChargingStationVariableValues = new ArrayList<>();
		
		final Set<ChargingStationVariable> notExistingVariables = new HashSet<>();
		
		chargingStationVariables.forEach((chargingStationVariable) -> {
			final Pair<String, Optional<String>> key = 
					new Pair<>(chargingStationVariable.variableName(), chargingStationVariable.variableInstance());
			
			final ChargingStationVariableEntity existingVariable = existingVariables.remove(key);
			if (existingVariable == null) {
				notExistingVariables.add(chargingStationVariable);
			} else {
				this.updateChargingStationVariableEntity(existingVariable, chargingStationVariable);
				
				mergedChargingStationVariableValues.add(this.chargingStationVariableValueRepository.mergeChargingStationVariableValues(
						existingVariable,
						chargingStationVariable.chargingStationVariableValues())
				);
			}
			
		});
		
		if (mergedChargingStationVariableValues.size() > 0) {
			return Uni.combine().all().unis(mergedChargingStationVariableValues)
					.usingConcurrencyOf(1)
					.discardItems()
					.chain(() -> createChargingStationVariables(chargingStationEntity, notExistingVariables))
					.replaceWithVoid();
		} else {
			return createChargingStationVariables(chargingStationEntity, notExistingVariables)
					.replaceWithVoid();
		}
	}
	
	public Uni<List<ChargingStationVariable>> createChargingStationVariables(
			final ChargingStationEntity chargingStationEntity,
			final Collection<ChargingStationVariable> chargingStationVariables) {
		
		return Multi.createFrom().items(chargingStationVariables.stream())
				.onItem()
				.transformToUni((chargingStationVariable) -> 
					this.createChargingStationVariableEntity(chargingStationEntity, chargingStationVariable)
						.call((chargingStationVariableEntity) -> 
							this.chargingStationVariableValueRepository.createChargingStationVariableValueEntities(
									chargingStationVariableEntity,
									chargingStationVariable.chargingStationVariableValues())
						)
						.map((chargingStationVariableEntity) -> 
							this.entityMapper.createChargingStationVariable(
									chargingStationVariableEntity))
				)
				.merge(1)
				.collect()
				.asList();
	}
	
	private Uni<ChargingStationVariableEntity> createChargingStationVariableEntity(
			final ChargingStationEntity chargingStationEntity, final ChargingStationVariable chargingStationVariable) {
		
		final ChargingStationVariableEntity chargingStationVariableEntity = 
				this.entityMapper.createChargingStationVariableEntity(chargingStationVariable);
		chargingStationVariableEntity.setChargingStation(chargingStationEntity);
		
		return persist(chargingStationVariableEntity);
	}

	private Uni<ChargingStationVariableEntity> getChargingStationVariableEntity(final Long chargingStationId,
			final String variableName, final Optional<String> variableInstance) {
		
		return find("chargingStation.id = ?1 and variableName = ?2 and variableInstance = ?3", 
				chargingStationId, variableName, variableInstance.orElse(null)).singleResult();
	}
	
	
	private void updateChargingStationVariableEntity(final ChargingStationVariableEntity chargingStationVariableEntity,
			final ChargingStationVariable chargingStationVariable) {
		
		chargingStationVariableEntity.setComponentName(chargingStationVariable.componentName().orElse(null));
		chargingStationVariableEntity.setComponentInstance(chargingStationVariable.componentInstance().orElse(null));
		chargingStationVariableEntity.setChargingStationEvseId(chargingStationVariable.chargingStationEvseId().orElse(null));
		chargingStationVariableEntity.setEvseConnectorId(chargingStationVariable.evseConnectorId().orElse(null));
		chargingStationVariableEntity.setVariableName(chargingStationVariable.variableName());
		chargingStationVariableEntity.setVariableInstance(chargingStationVariable.variableInstance().orElse(null));
		chargingStationVariableEntity.setUnit(chargingStationVariable.unit().orElse(null));
		chargingStationVariableEntity.setDataType(chargingStationVariable.dataType().orElse(null));
		chargingStationVariableEntity.setMinLimit(chargingStationVariable.minLimit().orElse(null));
		chargingStationVariableEntity.setMaxLimit(chargingStationVariable.maxLimit().orElse(null));
		chargingStationVariableEntity.setValuesList(chargingStationVariable.valuesList().orElse(null));
		chargingStationVariableEntity.setSupportsMonitoring(chargingStationVariable.supportsMonitoring().orElse(null));
		
	}
}