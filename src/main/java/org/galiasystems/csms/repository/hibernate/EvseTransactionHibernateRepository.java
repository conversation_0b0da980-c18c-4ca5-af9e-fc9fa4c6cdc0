package org.galiasystems.csms.repository.hibernate;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.function.Consumer;

import org.galiasystems.csms.management.filter.QueryFilter;
import org.galiasystems.csms.management.model.EvseTransaction;
import org.galiasystems.csms.management.repository.EvseTransactionRepository;
import org.galiasystems.csms.repository.hibernate.criteriaBuilder.CriteriaQueryHelper;
import org.galiasystems.csms.repository.hibernate.model.EvseTransactionEntity;
import org.galiasystems.csms.repository.hibernate.utils.HibernateRepositoryEntityMapper;

import io.quarkus.hibernate.reactive.panache.PanacheRepository;
import io.quarkus.hibernate.reactive.panache.common.WithSession;
import io.quarkus.hibernate.reactive.panache.common.WithTransaction;
import io.smallrye.mutiny.Multi;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.NoResultException;

import org.hibernate.reactive.mutiny.Mutiny;

@ApplicationScoped
public class EvseTransactionHibernateRepository implements EvseTransactionRepository, PanacheRepository<EvseTransactionEntity> {
	
	@Inject
	private HibernateRepositoryEntityMapper entityMapper;
	
	@Inject
    private CriteriaQueryHelper criteriaQueryHelper;

	@Inject
	private Mutiny.SessionFactory sessionFactory;

	@Override
	@WithTransaction
	public Uni<EvseTransaction> createEvseTransaction(final EvseTransaction evseTransaction) {

		final EvseTransactionEntity transactionEntity = this.entityMapper.createEvseTransactionEntity(evseTransaction);
		return persist(transactionEntity)
				.map(this.entityMapper::createEvseTransaction);
	}
	
	@Override
	@WithSession
	public Uni<Optional<EvseTransaction>> getEvseTransaction(final Long id) {
		
	    return findById(id)
				.map(this.entityMapper::createEvseTransaction)
				.map(Optional::ofNullable);
	}
	
	@Override
	@WithTransaction
    public Uni<EvseTransaction> updateEvseTransaction(final Long id, final Consumer<EvseTransaction> changeCallback) {
        
	    return findById(id)
                .onItem()
                .ifNull()
                .failWith(new NoResultException("Evse transaction with id = " + id + " not found for update."))
                .map((evseTransactionEntity) -> 
                    this.updateEvseTransactionEntity(evseTransactionEntity, changeCallback));
    }
	
	private EvseTransaction updateEvseTransactionEntity(final EvseTransactionEntity evseTransactionEntity, 
            final Consumer<EvseTransaction> changeCallback) {
        
        final EvseTransaction evseTransaction = this.entityMapper.createEvseTransaction(evseTransactionEntity);
        changeCallback.accept(evseTransaction);
        this.entityMapper.updateEvseTransactionEntity(evseTransactionEntity, evseTransaction);
        return this.entityMapper.createEvseTransaction(evseTransactionEntity);
    }
	
	@Override
	@WithSession
	public Uni<Optional<EvseTransaction>> findEvseTransaction(final QueryFilter queryFilter) {
		
	    return criteriaQueryHelper.find(
				EvseTransactionEntity.class,
				null,
				Collections.emptySet(),
                queryFilter,
                this.entityMapper::createEvseTransaction
        );
	}
	
	@Override
	@WithSession
	public Uni<List<EvseTransaction>> listEvseTransactions(final QueryFilter queryFilter) {
		
	    return criteriaQueryHelper.list(
				EvseTransactionEntity.class,
				null,
				Collections.emptySet(),
                queryFilter,
                this.entityMapper::createEvseTransactionList
        );
	}
	
	@Override
    public Multi<EvseTransaction> streamEvseTransactions(final QueryFilter queryFilter) {
        
        return criteriaQueryHelper.stream(
                EvseTransactionEntity.class,
                null,
                Collections.emptySet(),
                queryFilter,
                this.entityMapper::createEvseTransaction
        );
    }

	@Override
	@WithTransaction
	public Uni<Long> getNextEvseTransactionId() {
		
	    return sessionFactory.withSession(session ->
				session.createNativeQuery("SELECT nextval('transaction_id_seq')", Long.class)
						.getSingleResult()
		);
	}
}