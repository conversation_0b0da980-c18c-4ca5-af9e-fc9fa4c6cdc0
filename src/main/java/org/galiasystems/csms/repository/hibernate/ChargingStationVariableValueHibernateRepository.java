package org.galiasystems.csms.repository.hibernate;

import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.galiasystems.csms.management.model.ChargingStationVariableValue;
import org.galiasystems.csms.management.model.enums.VariableValueType;
import org.galiasystems.csms.management.repository.ChargingStationVariableValueRepository;
import org.galiasystems.csms.repository.hibernate.model.ChargingStationVariableEntity;
import org.galiasystems.csms.repository.hibernate.model.ChargingStationVariableValueEntity;
import org.galiasystems.csms.repository.hibernate.utils.HibernateRepositoryEntityMapper;

import io.quarkus.hibernate.reactive.panache.PanacheRepository;
import io.quarkus.hibernate.reactive.panache.common.WithTransaction;
import io.smallrye.mutiny.Multi;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.NoResultException;

@ApplicationScoped
public class ChargingStationVariableValueHibernateRepository implements ChargingStationVariableValueRepository,
				PanacheRepository<ChargingStationVariableValueEntity> {

	@Inject
	private HibernateRepositoryEntityMapper entityMapper;
	
	@Override
	@WithTransaction
	public Uni<List<ChargingStationVariableValue>> upsertChargingStationVariableValues(final Long chargingStationVariableId,
			final Collection<ChargingStationVariableValue> chargingStationVariableValues) {
		
		return Multi.createFrom().items(chargingStationVariableValues.stream())
				.onItem()
				.transformToUni((chargingStationVariableValue) -> 
					this.getChargingStationVariableValueEntity(chargingStationVariableId, chargingStationVariableValue.type().get())
						.onItem()
						.invoke((chargingStationVariableValueEntity) -> 
							this.updateChargingStationVariableValueEntity(chargingStationVariableValueEntity, 
									chargingStationVariableValue))
						.onFailure(NoResultException.class)
						.recoverWithUni(() -> {
							final ChargingStationVariableEntity chargingStationVariableEntity = new ChargingStationVariableEntity(chargingStationVariableId);
							return this.createChargingStationVariableValueEntity(chargingStationVariableEntity, chargingStationVariableValue);
						})
						.onItem()
						.transform((chargingStationVariableValueEntity) -> 
							this.entityMapper.createChargingStationVariableValue(chargingStationVariableValueEntity)
						)
				)
				.merge(1)
				.collect()
				.asList();
	}
	
	@WithTransaction
	public Uni<Void> mergeChargingStationVariableValues(
			final ChargingStationVariableEntity chargingStationVariableEntity,
			final Collection<ChargingStationVariableValue> chargingStationVariableValues) {
		
		final Map<VariableValueType, ChargingStationVariableValueEntity> existingVariableValues = new HashMap<>();
		
		chargingStationVariableEntity.getChargingStationVariableValues().forEach((chargingStationVariableValueEntity) -> 
			existingVariableValues.put(chargingStationVariableValueEntity.getType().get(), chargingStationVariableValueEntity));
		
		final Set<ChargingStationVariableValue> notExistingVariableValues = new HashSet<>();
		
		chargingStationVariableValues.forEach((chargingStationVariableValue) -> {
			
			final ChargingStationVariableValueEntity existingVariableValue = 
					existingVariableValues.remove(chargingStationVariableValue.type().get());
			
			if (existingVariableValue == null) {
				notExistingVariableValues.add(chargingStationVariableValue);
			} else {
				this.updateChargingStationVariableValueEntity(existingVariableValue, chargingStationVariableValue);
			}
		});
		
		return this.createChargingStationVariableValueEntities(chargingStationVariableEntity, notExistingVariableValues)
			.replaceWithVoid();
	}
	
	public Uni<List<ChargingStationVariableValue>> createChargingStationVariableValueEntities(
			final ChargingStationVariableEntity chargingStationVariableEntity,
			final Collection<ChargingStationVariableValue> chargingStationVariableValues) {
		
		return Multi.createFrom().items(chargingStationVariableValues.stream())
				.onItem()
				.transformToUni((chargingStationVariableValue) -> 
					this.createChargingStationVariableValueEntity(chargingStationVariableEntity, chargingStationVariableValue)
						.onItem()
						.transform((chargingStationVariableValueEntity) -> 
							this.entityMapper.createChargingStationVariableValue(chargingStationVariableValueEntity)
						)
				)
				.merge(1)
				.collect()
				.asList();
		
	}

	private Uni<ChargingStationVariableValueEntity> createChargingStationVariableValueEntity(
			final ChargingStationVariableEntity chargingStationVariableEntity, 
			final ChargingStationVariableValue chargingStationVariableValue) {
		
		final ChargingStationVariableValueEntity chargingStationVariableValueEntity = 
				this.entityMapper.createChargingStationVariableValueEntity(chargingStationVariableValue);
		chargingStationVariableValueEntity.setChargingStationVariable(chargingStationVariableEntity);
		
		return persist(chargingStationVariableValueEntity)
				.invoke((createdItem) -> chargingStationVariableEntity.addChargingStationVariableValue(createdItem));
	}
	
	private Uni<ChargingStationVariableValueEntity> getChargingStationVariableValueEntity(final Long chargingStationVariableId,
			final VariableValueType variableValueType) {
		
		return find("chargingStationVariable.id = ?1 and type = ?2", chargingStationVariableId, variableValueType).singleResult();
	}
	
	public void updateChargingStationVariableValueEntity(
			final ChargingStationVariableValueEntity chargingStationVariableValueEntity,
			final ChargingStationVariableValue chargingStationVariableValue) {
		
		chargingStationVariableValueEntity.setType(chargingStationVariableValue.type().orElse(null));
		chargingStationVariableValueEntity.setValue(chargingStationVariableValue.value().orElse(null));
		chargingStationVariableValueEntity.setMutability(chargingStationVariableValue.mutability().orElse(null));
	}

}