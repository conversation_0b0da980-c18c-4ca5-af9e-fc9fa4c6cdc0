package org.galiasystems.csms.repository.hibernate;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.function.Consumer;

import org.galiasystems.csms.management.filter.QueryFilter;
import org.galiasystems.csms.management.model.PowerGridAccess;
import org.galiasystems.csms.management.repository.PowerGridAccessRepository;
import org.galiasystems.csms.repository.hibernate.criteriaBuilder.CriteriaQueryHelper;
import org.galiasystems.csms.repository.hibernate.model.PowerGridAccessEntity;
import org.galiasystems.csms.repository.hibernate.utils.HibernateRepositoryEntityMapper;

import io.quarkus.hibernate.reactive.panache.PanacheRepository;
import io.quarkus.hibernate.reactive.panache.common.WithSession;
import io.quarkus.hibernate.reactive.panache.common.WithTransaction;
import io.smallrye.mutiny.Multi;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.NoResultException;

@ApplicationScoped
public class PowerGridAccessHibernateRepository implements PowerGridAccessRepository, 
                PanacheRepository<PowerGridAccessEntity> {
    
    @Inject
    private HibernateRepositoryEntityMapper entityMapper;
    
    @Inject
    private CriteriaQueryHelper criteriaQueryHelper;

    @Override
    @WithTransaction
    public Uni<PowerGridAccess> createPowerGridAccess(final PowerGridAccess powerGridAccess) {
    
        final PowerGridAccessEntity powerGridAccessEntity = 
                this.entityMapper.createPowerGridAccessEntity(powerGridAccess);
        return persist(powerGridAccessEntity)
                .map(this.entityMapper::createPowerGridAccess);
    }

    @Override
    @WithSession
    public Uni<Optional<PowerGridAccess>> getPowerGridAccess(final Long id) {
        
        return findById(id)
                .map(this.entityMapper::createPowerGridAccess)
                .map(Optional::ofNullable);
    }

    @Override
    @WithTransaction
    public Uni<PowerGridAccess> updatePowerGridAccess(final Long id, final Consumer<PowerGridAccess> changeCallback) {
    
        return findById(id)
                .onItem()
                .ifNull()
                .failWith(new NoResultException("PowerGrid access with id = " + id + " not found for update."))
                .map((powerGridAccessEntity) -> 
                    this.updatePowerGridAccessEntity(powerGridAccessEntity, changeCallback));
    }
    
    private PowerGridAccess updatePowerGridAccessEntity(final PowerGridAccessEntity powerGridAccessEntity, 
            final Consumer<PowerGridAccess> changeCallback) {
        
        final PowerGridAccess powerGridAccess = this.entityMapper.createPowerGridAccess(powerGridAccessEntity);
        changeCallback.accept(powerGridAccess);
        this.entityMapper.updatePowerGridAccessEntity(powerGridAccessEntity, powerGridAccess);
        return this.entityMapper.createPowerGridAccess(powerGridAccessEntity);
    }
    
    @Override
    @WithTransaction
    public Uni<Boolean> removePowerGridAccess(final Long id) {
        
        return deleteById(id);
    }

    @Override
    @WithSession
    public Uni<Optional<PowerGridAccess>> findPowerGridAccess(final QueryFilter queryFilter) {
        
        return criteriaQueryHelper.find(
                PowerGridAccessEntity.class,
                null,
                Collections.emptySet(),
                queryFilter,
                this.entityMapper::createPowerGridAccess
        );
    }

    @Override
    @WithSession
    public Uni<List<PowerGridAccess>> listPowerGridAccesses(final QueryFilter queryFilter) {
        
        return criteriaQueryHelper.list(
                PowerGridAccessEntity.class,
                null,
                Collections.emptySet(),
                queryFilter,
                this.entityMapper::createPowerGridAccessList
        );
    }
    
    @Override
    public Multi<PowerGridAccess> streamPowerGridAccesses(final QueryFilter queryFilter) {
        
        return criteriaQueryHelper.stream(
                PowerGridAccessEntity.class,
                null,
                Collections.emptySet(),
                queryFilter,
                this.entityMapper::createPowerGridAccess
        );
    }
}
