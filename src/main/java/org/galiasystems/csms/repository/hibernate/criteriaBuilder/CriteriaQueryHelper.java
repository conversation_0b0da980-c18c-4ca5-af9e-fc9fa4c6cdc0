package org.galiasystems.csms.repository.hibernate.criteriaBuilder;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.EntityGraph;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaDelete;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Order;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;

import org.apache.commons.lang3.function.TriConsumer;
import org.galiasystems.csms.management.filter.AttributeFilter;
import org.galiasystems.csms.management.filter.QueryFilter;
import org.galiasystems.csms.management.filter.QueryFilterOrder;
import org.galiasystems.csms.management.model.enums.attribute.BusinessEntityAttribute;
import org.hibernate.Filter;
import org.hibernate.query.criteria.JpaOrder;
import org.hibernate.reactive.mutiny.Mutiny.SelectionQuery;
import org.hibernate.reactive.mutiny.Mutiny.SessionFactory;
import org.hibernate.reactive.mutiny.Mutiny.Session;
import org.jboss.logging.Logger;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;

import io.smallrye.mutiny.Multi;
import io.smallrye.mutiny.Uni;

/**
 * Helper class for creating and executing JPA Criteria API queries with filtering based on AttributeFilters.
 * This class simplifies the process of building dynamic queries with various filter conditions.
 */
@ApplicationScoped
public class CriteriaQueryHelper {

    private final Logger log;
    private final SessionFactory sessionFactory;
    private final CriteriaFactory criteriaFactory;

    @Inject
    public CriteriaQueryHelper(final Logger log, final SessionFactory sessionFactory, 
            final CriteriaFactory criteriaFactory) {
        
        this.log = log;
        this.sessionFactory = sessionFactory;
        this.criteriaFactory = criteriaFactory;
    }
    
    /**
     * Creates a CriteriaQuery with filtering based on AttributeFilters, executes it.
     * This method handles pagination parameters and applies them to the query.
     *
     * @param <T>                         Type of the root entity of the query
     * @param <R>                         Type of the result entity of the query
     * @param entityClass                 Root class for the query.
     * @param resultClass                 Result class for the query.
     * @param entityGraphName             Name of the entity graph for the root class. Can be empty.
     * @param filterProviders             Apply all defined filter in the list. Can be empty collection.
     * @param queryFilter                 Query filter containing filters and pagination parameters
     * @param criteriaQueryConfigurator   Hook to be able to add custom logic to the criteria query
     * @return Uni containing the query results
     */
    public <T, R> Uni<List<R>> list(
            final Class<T> entityClass,
            final Class<R> resultClass,
            final String entityGraphName,
            final Collection<FilterProvider> filterProviders,
            final QueryFilter queryFilter,
            final Optional<TriConsumer<CriteriaBuilder, CriteriaQuery<R>, Root<T>>> criteriaQueryConfigurator
    ) {
        final CriteriaQuery<R> cq = createQuery(queryFilter, entityClass, resultClass, criteriaQueryConfigurator).criteriaQuery();

        
        final Integer limit = queryFilter != null ? queryFilter.getLimit() : null;
        final Integer offset = queryFilter != null ? queryFilter.getOffset() : null;
        
        return sessionFactory.withSession(session -> {
            
            this.enableFilters(session, filterProviders);

            final SelectionQuery<R> query = session.createQuery(cq);
            if (entityGraphName != null) {
                final EntityGraph<R> entityGraph = session.getEntityGraph(resultClass, entityGraphName);
                query.setPlan(entityGraph);
            }

            // Apply pagination if parameters are provided
            if (limit != null && limit > 0) {
                query.setMaxResults(limit);
            }

            if (offset != null && offset >= 0) {
                query.setFirstResult(offset);
            }

            return query.getResultList();
        });
    }
    
    /**
     * Creates a CriteriaQuery with filtering based on AttributeFilters and executes it.
     * This method handles pagination parameters and applies them to the query.
     *
     * @param <T>                         Type of the root entity of the query
     * @param <R>                         Type of the result entity of the query
     * @param entityClass                 Root class for the query.
     * @param resultClass                 Result class for the query.
     * @param entityGraphName             Name of the entity graph for the root class. Can be empty.
     * @param filterProviders             Apply all defined filter in the list. Can be empty collection.
     * @param queryFilter                 Query filter containing filters and pagination parameters
     * @param criteriaQueryConfigurator   Hook to be able to add custom logic to the criteria query
     * @return Multi containing the query results
     */
    public <T, R> Multi<R> stream(
            final Class<T> entityClass,
            final Class<R> resultClass,
            final String entityGraphName,
            final Collection<FilterProvider> filterProviders,
            final QueryFilter queryFilter,
            final Optional<TriConsumer<CriteriaBuilder, CriteriaQuery<R>, Root<T>>> criteriaQueryConfigurator
    ) {
        return list(entityClass, resultClass, entityGraphName, filterProviders, queryFilter, criteriaQueryConfigurator)
                .onItem().transformToMulti(Multi.createFrom()::iterable);
    }

    /**
     * Creates a CriteriaQuery with filtering based on AttributeFilters, executes it.
     * This method handles pagination parameters and applies them to the query.
     *
     * @param <T>               Type of the entities being queried
     * @param entityClass       Root class for the query.
     * @param entityGraphName   Name of the entity graph for the root class. Can be empty.
     * @param filterProviders   Apply all defined filter in the list. Can be empty collection.
     * @param queryFilter       Query filter containing filters and pagination parameters
     * @return Uni containing the query results
     */
	public <T> Uni<List<T>> list(
            final Class<T> entityClass,
            final String entityGraphName,
            final Collection<FilterProvider> filterProviders,
            final QueryFilter queryFilter
    ) {
        return list(entityClass, entityClass, entityGraphName, filterProviders, queryFilter, Optional.empty());
    }
	
	/**
     * Creates a CriteriaQuery with filtering based on AttributeFilters, executes it, and transforms the results.
     * This method handles pagination parameters and applies them to the query.
     *
     * @param <T>               Type of the entities being queried
     * @param <R>               Result type after transformation
     * @param entityClass       Root class for the query.
     * @param entityGraphName   Name of the entity graph for the root class. Can be empty.
     * @param filterProviders   Apply all defined filter in the list. Can be empty collection.
     * @param queryFilter       Query filter containing filters and pagination parameters
     * @param transformer       Function to transform the query results
     * @return Uni containing the transformed query results
     */
    public <T, R> Uni<List<R>> list(
            final Class<T> entityClass,
            final String entityGraphName,
            final Collection<FilterProvider> filterProviders,
            final QueryFilter queryFilter,
            final Function<List<T>, List<R>> transformer
    ) {
        return list(entityClass, entityGraphName, filterProviders, queryFilter)
                .onItem()
                .transform(transformer);
    }
    
    /**
     * Creates a CriteriaQuery with filtering based on AttributeFilters, executes it, and transforms the results.
     * This method handles pagination parameters and applies them to the query.
     *
     * @param <T>               Type of the entities being queried
     * @param <R>               Result type after transformation
     * @param entityClass       Root class for the query.
     * @param entityGraphName   Name of the entity graph for the root class. Can be empty.
     * @param filterProviders   Apply all defined filter in the list. Can be empty collection.
     * @param queryFilter       Query filter containing filters and pagination parameters
     * @param transformer       Function to transform the query result items.
     * @return Multi containing the transformed query results
     */
    public <T, R> Multi<R> stream(
            final Class<T> entityClass,
            final String entityGraphName,
            final Collection<FilterProvider> filterProviders,
            final QueryFilter queryFilter,
            final Function<T, R> transformer
    ) {
        return list(entityClass, entityGraphName, filterProviders, queryFilter)
                .onItem().transformToMulti(Multi.createFrom()::iterable)
                .map(transformer);
    }
    
    /**
     * Creates a CriteriaQuery with filtering based on AttributeFilters, executes it, and transforms the result.
     * 
     * @param <T>               Type of the entity being queried.
     * @param <R>               Result type after transformation.
     * @param entityClass       Root class for the query.
     * @param entityGraphName   Name of the entity graph for the root class. Can be empty.
     * @param filterProviders   Apply all defined filter in the list. Can be empty collection.
     * @param queryFilter       Query filter containing filters.
     * @param transformer       Function to transform the query result.
     * @return Uni containing the transformed query result.
     */
    public <T, R> Uni<Optional<R>> find(
            final Class<T> entityClass,
            final String entityGraphName,
            final Collection<FilterProvider> filterProviders,
            final QueryFilter queryFilter,
            final Function<T, R> transformer
    ) {
            return this.findNullable(entityClass, entityGraphName, filterProviders, queryFilter)
                    .map(transformer)
                    .map(Optional::ofNullable);
    }
    
    /**
     * Creates a CriteriaQuery with filtering based on AttributeFilters and executes it.
     * 
     * @param <T>               Type of the entity being queried
     * @param entityClass       Root class for the query.
     * @param entityGraphName   Name of the entity graph for the root class. Can be empty.
     * @param filterProviders   Apply all defined filter in the list. Can be empty collection.
     * @param queryFilter       Query filter containing filters.
     * @return Uni containing the query result.
     */
    public <T> Uni<Optional<T>> find(
            final Class<T> entityClass,
            final String entityGraphName,
            final Collection<FilterProvider> filterProviders,
            final QueryFilter queryFilter
    ) {
        return this.findNullable(entityClass, entityGraphName, filterProviders, queryFilter)
                    .map(Optional::ofNullable);
    }
    
    /**
     * Creates a CriteriaQuery with filtering based on AttributeFilters and executes it.
     * 
     * @param <T>               Type of the entity being queried
     * @param entityClass       Root class for the query.
     * @param entityGraphName   Name of the entity graph for the root class. Can be empty.
     * @param filterProviders   Apply all defined filter in the list. Can be empty collection.
     * @param queryFilter       Query filter containing filters.
     * @return Uni containing the query result.
     */
    public <T> Uni<T> findNullable(
            final Class<T> entityClass,
            final String entityGraphName,
            final Collection<FilterProvider> filterProviders,
            final QueryFilter queryFilter
    ) {
        final CriteriaQuery<T> cq = createQuery(queryFilter, entityClass, entityClass, Optional.empty()).criteriaQuery();
        
        return sessionFactory.withSession(session -> {
            
            this.enableFilters(session, filterProviders);
            
            final SelectionQuery<T> query = session.createQuery(cq);
            if (entityGraphName != null) {
                final EntityGraph<T> entityGraph = session.getEntityGraph(entityClass, entityGraphName);
                query.setPlan(entityGraph);
            }

            return query.getSingleResultOrNull();
        });
    }
    
    /**
     * Creates a count CriteriaQuery with filtering based on AttributeFilters, 
     * executes it and returns the number of elements matching to the filter criteria.
     *
     * @param <T>               Type of the entities being queried
     * @param entityClass       Root class for the query
     * @param filterProviders   Apply all defined filter in the list. Can be empty collection.
     * @param queryFilter       Query filter containing filters and pagination parameters
     * @return Uni containing the number of elements matching to the filter criteria.
     */
    public <T> Uni<Long> count(
            final Class<T> entityClass,
            final Collection<FilterProvider> filterProviders,
            final QueryFilter queryFilter
    ) {
        final CreateQueryResult<T, Long> createQueryResult = createQuery(queryFilter, entityClass, Long.class, Optional.empty());

        return sessionFactory.withSession(session -> {
            
            this.enableFilters(session, filterProviders);
            
        	final CriteriaBuilder cb = createQueryResult.criteriaBuilder();
        	final CriteriaQuery<Long> cq = createQueryResult.criteriaQuery();
        	final Root<T> root = createQueryResult.root();

        	final SelectionQuery<Long> query = session.createQuery(cq.select(cb.count(root)));

            return query.getSingleResult();
        });
    }
    
    /**
     * Creates a CriteriaQuery with filtering based on AttributeFilters, 
     * executes it and checks if there exists any entities what are fulfill the given filtering.
     * If exists returns true if not exists returns false.
     *
     * @param <T>               Type of the entities being queried
     * @param entityClass       Root class for the query
     * @param filterProviders   Apply all defined filter in the list. Can be empty collection.
     * @param queryFilter       Query filter containing filters and pagination parameters
     * @return Uni<Boolean> True if there exists any entities what are fulfill the given filtering, false otherwise.
     */
    public <T> Uni<Boolean> hasResult(
            final Class<T> entityClass,
            final Collection<FilterProvider> filterProviders,
            final QueryFilter queryFilter
    ) {
        final CriteriaQuery<T> cq = createQuery(queryFilter, entityClass, entityClass, Optional.empty()).criteriaQuery();

        return sessionFactory.withSession(session -> {
            
                this.enableFilters(session, filterProviders);

                final SelectionQuery<T> query = session.createQuery(cq);
                query.setMaxResults(1);

            return query.getSingleResult()
                    .map((result) -> result != null);
        });
    }
    
    /**
     * Creates a CriteriaDelete with filtering based on AttributeFilters, 
     * executes it and returns the number of the deleted entities.
     * 
     * @param <T>           Type of the entities being deleted.
     * @param entityClass   Target class for the delete.
     * @param queryFilter   Query filter containing filters for the delete.
     * @return Then number of entities were deleted.
     */
    public <T> Uni<Integer> delete(final Class<T> entityClass, final QueryFilter queryFilter) {
        
        final CriteriaDelete<T> cd = this.createCriteriaDelete(queryFilter, entityClass);
        
        return sessionFactory.withSession(session -> {
            
            return session.createQuery(cd).executeUpdate();
        });
    }
    
    /**
     * Enable the given filters on the session.
     * 
     * @param session The hibernate session instance.
     * @param filterProviders The filter definitions to apply.
     */
    public void enableFilters(final Session session, final Collection<FilterProvider> filterProviders) {
        filterProviders.forEach((filterProvider) -> {
            final Filter filter = session.enableFilter(filterProvider.getFilterName());
            filterProvider.getFilterUpdater().accept(filter);
        });
    }

    /**
     * Creates a CriteriaQuery with filtering based on AttributeFilters.
     *
     * @param <T>         Type of the entities being queried
     * @param <R>         Result type of the query
     * @param queryFilter Contains the List of AttributeFilters to apply to the query.
     * @param entityClass Root class for the query
     * @return CriteriaQuery with filtering applied
     */
    private <T, R> CreateQueryResult<T, R> createQuery(final QueryFilter queryFilter, final Class<T> entityClass, 
    		final Class<R> resultClass, final Optional<TriConsumer<CriteriaBuilder, CriteriaQuery<R>, Root<T>>> criteriaQueryConfigurator) {

        final CriteriaBuilder cb = sessionFactory.getCriteriaBuilder();
        final CriteriaQuery<R> cq = cb.createQuery(resultClass);
        final Root<T> root = cq.from(entityClass);
   
        final CriteriaContext<T> context = new CriteriaContext<>(cb, cq, root);
        
        final List<Predicate> predicates = this.createPredicates(queryFilter, context);
        if (!predicates.isEmpty()) {
            cq.where(predicates.toArray(new Predicate[0]));
        }
        
        this.applyOrderByToCriteriaQuery(queryFilter, context, cq);
        
        criteriaQueryConfigurator.ifPresent((providedConfigurator) -> providedConfigurator.accept(cb, cq, root));

        return new CreateQueryResult<T, R>(cb, cq, root);
    }
    
    /**
     * Creates a CriteriaDelete with filtering based on AttributeFilters.
     *
     * @param <T>         Type of the entities being deleted.
     * @param queryFilter Contains the List of AttributeFilters to apply to the delete query.
     * @param entityClass Target class of the delete
     * @return CriteriaDelete with filtering applied.
     */
    private <T, R> CriteriaDelete<T> createCriteriaDelete(final QueryFilter queryFilter, final Class<T> entityClass) {

        final CriteriaBuilder cb = sessionFactory.getCriteriaBuilder();
        final CriteriaDelete<T> cd = cb.createCriteriaDelete(entityClass);
        final Root<T> root = cd.from(entityClass);
   
        final CriteriaContext<T> context = new CriteriaContext<>(cb, cd, root);
        
        final List<Predicate> predicates = this.createPredicates(queryFilter, context);
        if (!predicates.isEmpty()) {
            cd.where(predicates.toArray(new Predicate[0]));
        }

        return cd;
    }
    
    /**
     * Create predicates based on the query filter.
     * 
     * @param <T> Type of the entity being queried.
     * @param queryFilter Contains the List of AttributeFilters to apply to the query.
     * @param context Contains all objects what is required to create a query builder expression.
     * @return Predicates for the filtering.
     */
    private  <T> List<Predicate> createPredicates(final QueryFilter queryFilter, 
            final CriteriaContext<T> context) {
        
        final List<Predicate> predicates = new ArrayList<>();
        
        final List<AttributeFilter> filters = queryFilter != null ? queryFilter.getFilters() : Collections.emptyList();
        if (filters != null && !filters.isEmpty()) {
            
       
            for (AttributeFilter filter : filters) {
                try {
                    final List<Predicate> newPredicates = this.criteriaFactory.createCriteria(context, filter);
                    predicates.addAll(newPredicates);
                } catch (Exception e) {
                    log.warn("Failed to apply filter: " + filter, e);
                }
            }
        }
        
        return predicates;
    }
    
    /**
     * Apply the orderBy expression to the query.
     * 
     * @param <T> Type of the entity being queried.
     * @param <R> Result type of the query.
     * @param queryFilter Contains the List of orders to apply to the query.
     * @param context Contains all objects what is required to create a query builder expression.
     * @param cq The query object.
     */
    private <T, R> void applyOrderByToCriteriaQuery(final QueryFilter queryFilter, 
            final CriteriaContext<T> context, final CriteriaQuery<R> cq) {
        
        final List<QueryFilterOrder> queryFilterOrders = queryFilter != null ? queryFilter.getOrders() : Collections.emptyList();
        if (queryFilterOrders != null && !queryFilterOrders.isEmpty()) {
            final List<Order> orders = new ArrayList<>();

             for (QueryFilterOrder queryFilterOrder : queryFilterOrders) {
                 try {
                     final BusinessEntityAttribute attributeDef = queryFilterOrder.getAttributeDefinition();
                     
                     final JpaOrder order = this.criteriaFactory.order(context, attributeDef, queryFilterOrder.getSortDirection(), 
                             queryFilterOrder.getNullOrder());
                     orders.add(order);

                 } catch (Exception e) {
                     log.warn("Failed to apply order: " + queryFilterOrder, e);
                 }
             }

             if (!orders.isEmpty()) {
                 cq.orderBy(orders);
             }
         }
    }
    
    private static record CreateQueryResult<T, R>(CriteriaBuilder criteriaBuilder, CriteriaQuery<R> criteriaQuery, Root<T> root) {
    };
}