package org.galiasystems.csms.repository.hibernate;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.function.Consumer;

import org.galiasystems.csms.management.filter.QueryFilter;
import org.galiasystems.csms.management.model.ChargingStation;
import org.galiasystems.csms.management.model.ChargingStationVariable;
import org.galiasystems.csms.management.model.enums.attribute.ChargingStationAttribute;
import org.galiasystems.csms.management.repository.ChargingStationRepository;
import org.galiasystems.csms.repository.hibernate.criteriaBuilder.CriteriaQueryHelper;
import org.galiasystems.csms.repository.hibernate.model.ChargingStationEntity;
import org.galiasystems.csms.repository.hibernate.utils.HibernateRepositoryEntityMapper;

import io.quarkus.hibernate.reactive.panache.PanacheRepository;
import io.quarkus.hibernate.reactive.panache.common.WithSession;
import io.quarkus.hibernate.reactive.panache.common.WithTransaction;
import io.smallrye.mutiny.Multi;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.NoResultException;


@ApplicationScoped
public class ChargingStationHibernateRepository extends HibernateRepositoryBase implements ChargingStationRepository,
                PanacheRepository<ChargingStationEntity> {

    @Inject
    private HibernateRepositoryEntityMapper entityMapper;
    
    @Inject
    private CriteriaQueryHelper criteriaQueryHelper;

	@Inject
	private ChargingStationVariableHibernateRepository chargingStationVariableRepository;


	@Override
	@WithTransaction
	public Uni<ChargingStation> createChargingStation(final ChargingStation chargingStation) {
		
	    final ChargingStationEntity chargingStationEntity = this.entityMapper.createChargingStationEntity(chargingStation);
	    return persist(chargingStationEntity)
	            .map(this.entityMapper::createChargingStation)
	            .invoke((persistedChargingStation) -> persistedChargingStation.setUserName(chargingStation.getUserName()));
	}
	
	@Override
    @WithSession
    public Uni<Optional<ChargingStation>> getChargingStation(final Long id) {

        return this.findChargingStationEntity(id, null)
                .map(this.entityMapper::createChargingStation)
                .map(Optional::ofNullable);
    }

	@Override
	@WithTransaction
	public Uni<ChargingStation> updateChargingStation(final Long id, final Consumer<ChargingStation> changeCallback) {
	    
	    return this.findChargingStationEntity(id, null)
	            .onItem()
	            .ifNull()
	            .failWith(new NoResultException("Charging station with id = " + id + " not found for update."))
	            .map((chargingStationEntity) -> 
	                this.updateChargingStationEntity(chargingStationEntity, changeCallback));
	}
	
	@Override
	@WithTransaction
    public Uni<ChargingStation> updateChargingStation(final QueryFilter queryFilter,
            final Consumer<ChargingStation> changeCallback) {
        
	    return this.criteriaQueryHelper.findNullable(ChargingStationEntity.class, null, Collections.emptySet(),
	                    this.addDefaultFiltering(queryFilter))
	            .onItem()
	            .ifNull()
                .failWith(new NoResultException("Charging station not found for update."))
                .map((connectorEntity) -> 
                    this.updateChargingStationEntity(connectorEntity, changeCallback));
    }
	
	private ChargingStation updateChargingStationEntity(final ChargingStationEntity chargingStationEntity, 
            final Consumer<ChargingStation> changeCallback) {
	    
	    final ChargingStation chargingStation = this.entityMapper.createChargingStation(chargingStationEntity);
        changeCallback.accept(chargingStation);
        this.entityMapper.updateChargingStationEntity(chargingStationEntity, chargingStation);
        return this.entityMapper.createChargingStation(chargingStationEntity);
    }
	
	@Override
    @WithTransaction
    public Uni<ChargingStation> updateChargingStationVariables(final Long chargingStationId,
            final Collection<ChargingStationVariable> chargingStationVariables) {

        return findChargingStationEntity(chargingStationId, ChargingStationEntity.ENTITY_GRAPH_VARIABLES_AND_VALUES)
                .call((chargingStationEntity) ->
                        this.chargingStationVariableRepository.mergeChargingStationVariables(chargingStationEntity,
                                chargingStationEntity.getChargingStationVariables(),
                                chargingStationVariables))
                .map(this.entityMapper::createChargingStation);
    }
	
	@Override
	@WithTransaction
    public Uni<ChargingStation> updateChargingStationVariables(final QueryFilter chargingStationQueryFilter,
            final Collection<ChargingStationVariable> chargingStationVariables) {

        return this.criteriaQueryHelper.findNullable(ChargingStationEntity.class, null, Collections.emptySet(),
                        this.addDefaultFiltering(chargingStationQueryFilter))
                .onItem()
                .ifNull()
                .failWith(new NoResultException("Charging station not found for update charging station variables."))
                .call((chargingStationEntity) ->
                    this.chargingStationVariableRepository.mergeChargingStationVariables(chargingStationEntity,
                            chargingStationEntity.getChargingStationVariables(),
                            chargingStationVariables))
                .map(this.entityMapper::createChargingStation);
    }
	
	private Uni<ChargingStationEntity> findChargingStationEntity(final Long id,
            final String entityGraph) {

	    final QueryFilter queryFilter = new QueryFilter()
                .addEqualFilter(ChargingStationAttribute.ID, id);
	    
        return criteriaQueryHelper.findNullable(
                ChargingStationEntity.class,
                entityGraph,
                Collections.emptySet(),
                this.addDefaultFiltering(queryFilter)
        );
    }
	
	@Override
	@WithSession
    public Uni<Optional<ChargingStation>> findChargingStation(final QueryFilter queryFilter) {
        
	    return criteriaQueryHelper.find(
	            ChargingStationEntity.class,
	            null,
	            Collections.emptySet(),
	            this.addDefaultFiltering(queryFilter),
                this.entityMapper::createChargingStation
        );
    }
	
	@Override
	@WithSession
    public Uni<List<ChargingStation>> listChargingStations(final QueryFilter queryFilter) {
        
	    return criteriaQueryHelper.list(
	            ChargingStationEntity.class,
	            null,
	            Collections.emptySet(),
	            this.addDefaultFiltering(queryFilter),
                this.entityMapper::createChargingStationList
        );
    }
	
	@Override
    public Multi<ChargingStation> streamChargingStations(final QueryFilter queryFilter) {
        
	    return criteriaQueryHelper.stream(
                ChargingStationEntity.class,
                null,
                Collections.emptySet(),
                this.addDefaultFiltering(queryFilter),
                this.entityMapper::createChargingStation
        );
    }
	
	private QueryFilter addDefaultFiltering(final QueryFilter queryFilter) {
        return queryFilter
            .addEqualFilter(ChargingStationAttribute.CHARGING_STATION_ACCESS_USER_ID, this.getUserId());
    }
}