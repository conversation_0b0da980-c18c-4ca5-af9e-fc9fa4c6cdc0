package org.galiasystems.csms.repository.hibernate;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.function.Consumer;

import org.galiasystems.csms.management.filter.QueryFilter;
import org.galiasystems.csms.management.model.Tenant;
import org.galiasystems.csms.management.repository.TenantRepository;
import org.galiasystems.csms.repository.hibernate.criteriaBuilder.CriteriaQueryHelper;
import org.galiasystems.csms.repository.hibernate.model.TenantEntity;
import org.galiasystems.csms.repository.hibernate.utils.HibernateRepositoryEntityMapper;

import io.quarkus.hibernate.reactive.panache.PanacheRepository;
import io.quarkus.hibernate.reactive.panache.common.WithSession;
import io.quarkus.hibernate.reactive.panache.common.WithTransaction;
import io.smallrye.mutiny.Multi;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.NoResultException;

@ApplicationScoped
public class TenantHibernateRepository implements TenantRepository, PanacheRepository<TenantEntity> {

    @Inject
    private HibernateRepositoryEntityMapper entityMapper;
    
    @Inject
    private CriteriaQueryHelper criteriaQueryHelper;

    @Override
    @WithTransaction
    public Uni<Tenant> createTenant(final Tenant tenant) {
        
        final TenantEntity tenantEntity = this.entityMapper.createTenantEntity(tenant);
        return persist(tenantEntity)
                .map(this.entityMapper::createTenant);
    }

    @Override
    @WithSession
    public Uni<Optional<Tenant>> getTenant(final Long id) {
        
        return findById(id)
                .map(this.entityMapper::createTenant)
                .map(Optional::ofNullable);
    }

    @Override
    @WithTransaction
    public Uni<Tenant> updateTenant(final Long id, final Consumer<Tenant> changeCallback) {
       
        return findById(id)
                .onItem()
                .ifNull()
                .failWith(new NoResultException("Tenant with id = " + id + " not found for update."))
                .map((tenantEntity) -> 
                    this.updateTenantEntity(tenantEntity, changeCallback));
    }
    
    private Tenant updateTenantEntity(final TenantEntity tenantEntity, 
            final Consumer<Tenant> changeCallback) {
        
        final Tenant tenant = this.entityMapper.createTenant(tenantEntity);
        changeCallback.accept(tenant);
        this.entityMapper.updateTenantEntity(tenantEntity, tenant);
        return this.entityMapper.createTenant(tenantEntity);
    }

    @Override
    @WithSession
    public Uni<Optional<Tenant>> findTenant(final QueryFilter queryFilter) {
        
        return criteriaQueryHelper.find(
                TenantEntity.class,
                null,
                Collections.emptySet(),
                queryFilter,
                this.entityMapper::createTenant
        );
    }

    @Override
    @WithSession
    public Uni<List<Tenant>> listTenants(final QueryFilter queryFilter) {
       
        return criteriaQueryHelper.list(
                TenantEntity.class,
                null,
                Collections.emptySet(),
                queryFilter,
                this.entityMapper::createTenantList
        );
    }
    
    @Override
    public Multi<Tenant> streamTenants(final QueryFilter queryFilter) {
       
        return criteriaQueryHelper.stream(
                TenantEntity.class,
                null,
                Collections.emptySet(),
                queryFilter,
                this.entityMapper::createTenant
        );
    }
}