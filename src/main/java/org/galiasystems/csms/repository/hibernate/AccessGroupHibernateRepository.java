package org.galiasystems.csms.repository.hibernate;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.function.Consumer;

import org.galiasystems.csms.management.filter.QueryFilter;
import org.galiasystems.csms.management.model.AccessGroup;
import org.galiasystems.csms.management.model.enums.OrganizationAccessRole;
import org.galiasystems.csms.management.model.enums.attribute.AccessGroupAttribute;
import org.galiasystems.csms.management.repository.AccessGroupRepository;
import org.galiasystems.csms.repository.hibernate.criteriaBuilder.CriteriaQueryHelper;
import org.galiasystems.csms.repository.hibernate.model.AccessGroupEntity;
import org.galiasystems.csms.repository.hibernate.utils.HibernateRepositoryEntityMapper;

import io.quarkus.hibernate.reactive.panache.PanacheRepository;
import io.quarkus.hibernate.reactive.panache.common.WithSession;
import io.quarkus.hibernate.reactive.panache.common.WithTransaction;
import io.smallrye.mutiny.Multi;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.NoResultException;

@ApplicationScoped
public class AccessGroupHibernateRepository extends HibernateRepositoryBase implements AccessGroupRepository, 
                PanacheRepository<AccessGroupEntity> {

    private static final List<OrganizationAccessRole> DEFAULT_ORGANIZATION_ACCESS_ROLE_FILTER_VALUES = 
            List.of(OrganizationAccessRole.Owner, OrganizationAccessRole.Admin);
    
    @Inject
    private HibernateRepositoryEntityMapper entityMapper;
    
    @Inject
    private CriteriaQueryHelper criteriaQueryHelper;
    
    @Override
    @WithTransaction
    public Uni<AccessGroup> createAccessGroup(final AccessGroup accessGroup) {
        
        final AccessGroupEntity accessGroupEntity = 
                this.entityMapper.createAccessGroupEntity(accessGroup);
        return persist(accessGroupEntity)
                .map(this.entityMapper::createAccessGroup);
    }

    @Override
    @WithSession
    public Uni<Optional<AccessGroup>> getAccessGroup(final Long id) {

        return findAccessGroupEntityById(id)
                .map(this.entityMapper::createAccessGroup)
                .map(Optional::ofNullable);
    }

    @Override
    @WithTransaction
    public Uni<AccessGroup> updateAccessGroup(final Long id,
            final Consumer<AccessGroup> changeCallback) {
        
        return findAccessGroupEntityById(id)
                .onItem()
                .ifNull()
                .failWith(new NoResultException("Access group with id = " + id + " not found for update."))
                .map((accessGroupEntity) -> 
                    this.updateAccessGroupEntity(accessGroupEntity, changeCallback));
    }
    
    private AccessGroup updateAccessGroupEntity(final AccessGroupEntity accessGroupEntity, 
            final Consumer<AccessGroup> changeCallback) {
        
        final AccessGroup accessGroup = this.entityMapper.createAccessGroup(accessGroupEntity);
        changeCallback.accept(accessGroup);
        this.entityMapper.updateAccessGroupEntity(accessGroupEntity, accessGroup);
        return this.entityMapper.createAccessGroup(accessGroupEntity);
    }
    
    private Uni<AccessGroupEntity> findAccessGroupEntityById(final Long id) {
        
        final QueryFilter queryFilter = new QueryFilter()
                .addEqualFilter(AccessGroupAttribute.ID, id);
        
        return criteriaQueryHelper.findNullable(
                AccessGroupEntity.class,
                null,
                Collections.emptySet(),
                this.addDefaultFiltering(queryFilter)
        );
    }

    @Override
    @WithSession
    public Uni<Optional<AccessGroup>> findAccessGroup(final QueryFilter queryFilter) {
        
        return criteriaQueryHelper.find(
                AccessGroupEntity.class,
                null,
                Collections.emptySet(),
                this.addDefaultFiltering(queryFilter),
                this.entityMapper::createAccessGroup
        );
    }

    @Override
    @WithSession
    public Uni<List<AccessGroup>> listAccessGroups(final QueryFilter queryFilter) {
        
        return criteriaQueryHelper.list(
                AccessGroupEntity.class,
                null,
                Collections.emptySet(),
                this.addDefaultFiltering(queryFilter),
                this.entityMapper::createAccessGroupList
        );
    }

    @Override
    public Multi<AccessGroup> streamAccessGroups(final QueryFilter queryFilter) {
        
        return criteriaQueryHelper.stream(
                AccessGroupEntity.class,
                null,
                Collections.emptySet(),
                this.addDefaultFiltering(queryFilter),
                this.entityMapper::createAccessGroup
        );
    }
    
    private QueryFilter addDefaultFiltering(final QueryFilter queryFilter) {
        return queryFilter
            .addEqualFilter(AccessGroupAttribute.ORGANIZATION_ACCESS_USER_ID, this.getUserId())
            .addInFilter(AccessGroupAttribute.ORGANIZATION_ACCESS_ROLE, DEFAULT_ORGANIZATION_ACCESS_ROLE_FILTER_VALUES);
    }
}
