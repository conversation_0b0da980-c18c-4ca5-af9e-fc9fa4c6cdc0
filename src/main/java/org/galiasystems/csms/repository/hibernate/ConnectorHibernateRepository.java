package org.galiasystems.csms.repository.hibernate;

import io.quarkus.hibernate.reactive.panache.PanacheRepository;
import io.quarkus.hibernate.reactive.panache.common.WithSession;
import io.quarkus.hibernate.reactive.panache.common.WithTransaction;
import io.smallrye.mutiny.Multi;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.NoResultException;
import org.galiasystems.csms.management.filter.QueryFilter;
import org.galiasystems.csms.management.model.Connector;
import org.galiasystems.csms.management.repository.ConnectorRepository;
import org.galiasystems.csms.repository.hibernate.criteriaBuilder.CriteriaQueryHelper;
import org.galiasystems.csms.repository.hibernate.model.ConnectorEntity;
import org.galiasystems.csms.repository.hibernate.utils.HibernateRepositoryEntityMapper;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.function.Consumer;

@ApplicationScoped
public class ConnectorHibernateRepository implements ConnectorRepository, PanacheRepository<ConnectorEntity> {

	private final HibernateRepositoryEntityMapper entityMapper;
	private final CriteriaQueryHelper criteriaQueryHelper;

	@Inject
	public ConnectorHibernateRepository(HibernateRepositoryEntityMapper entityMapper, CriteriaQueryHelper criteriaQueryHelper) {
		this.entityMapper = entityMapper;
		this.criteriaQueryHelper = criteriaQueryHelper;
	}

	@Override
	@WithTransaction
	public Uni<Connector> createConnector(final Connector connector) {
	    
		final ConnectorEntity connectorEntity = this.entityMapper.createConnectorEntity(connector);
		return persist(connectorEntity)
				.map(this.entityMapper::createConnector);
	}
	
	@Override
    @WithSession
    public Uni<Optional<Connector>> getConnector(final Long id) {
        
        return findById(id)
                .map(this.entityMapper::createConnector)
                .map(Optional::ofNullable);
    }
	
	@Override
    @WithTransaction
    public Uni<Connector> updateConnector(final Long id, final Consumer<Connector> changeCallback) {
                
	    return findById(id)
                .onItem()
                .ifNull()
                .failWith(new NoResultException("Connector with id = " + id + " not found for update."))
                .map((connectorEntity) -> 
                    this.updateConnectorEntity(connectorEntity, changeCallback));
    }
	
	@Override
    @WithTransaction
    public Uni<Connector> updateConnector(final QueryFilter queryFilter, final Consumer<Connector> changeCallback) {
                
        return this.criteriaQueryHelper.findNullable(ConnectorEntity.class, null, Collections.emptySet(), queryFilter)
                .onItem()
                .ifNull()
                .failWith(new NoResultException("Connector not found for update."))
                .map((connectorEntity) -> 
                    this.updateConnectorEntity(connectorEntity, changeCallback));
    }
	
	@Override
	@WithTransaction
    public Uni<Optional<Connector>> updateConnectorIfExists(final QueryFilter queryFilter, final Consumer<Connector> changeCallback) {
        
	    return this.criteriaQueryHelper.findNullable(ConnectorEntity.class, null, Collections.emptySet(), queryFilter)
                .onItem()
                .ifNotNull()
                .transform((connectorEntity) -> 
                    this.updateConnectorEntity(connectorEntity, changeCallback))
                .map((connector) -> Optional.ofNullable(connector));
    }

    @Override
    @WithTransaction
    public Uni<Void> updateConnectors(final QueryFilter queryFilter, final Consumer<Connector> changeCallback) {
        
        return this.criteriaQueryHelper.list(
                        ConnectorEntity.class,
                        null,
                        Collections.emptySet(),
                        queryFilter,
                        entities -> entities
                )
                .invoke((connectorEntities) -> 
                    connectorEntities.stream().forEach((connectorEntity) -> 
                        this.updateConnectorEntityWithoutTransform(connectorEntity, changeCallback))
                )
                .replaceWithVoid();
    }

    private Connector updateConnectorEntity(final ConnectorEntity connectorEntity, 
            final Consumer<Connector> changeCallback) {
        
        this.updateConnectorEntityWithoutTransform(connectorEntity, changeCallback);
        return this.entityMapper.createConnector(connectorEntity);
    }

    private void updateConnectorEntityWithoutTransform(final ConnectorEntity connectorEntity, 
            final Consumer<Connector> changeCallback) {
        
        final Connector connector = this.entityMapper.createConnector(connectorEntity);
        changeCallback.accept(connector);
        this.entityMapper.updateConnectorEntity(connectorEntity, connector);
    }
    
    @Override
    @WithSession
    public Uni<Optional<Connector>> findConnector(final QueryFilter queryFilter) {
        
        return this.criteriaQueryHelper.find(
                ConnectorEntity.class,
                null,
                Collections.emptySet(),
                queryFilter,
                this.entityMapper::createConnector
        );
    }

	@Override
	@WithSession
	public Uni<List<Connector>> listConnectors(final QueryFilter queryFilter) {
	    
		return this.criteriaQueryHelper.list(
				ConnectorEntity.class,
				null,
				Collections.emptySet(),
				queryFilter,
				this.entityMapper::createConnectorList
		);
	}
	
	@Override
    public Multi<Connector> streamConnectors(final QueryFilter queryFilter) {
        
        return this.criteriaQueryHelper.stream(
                ConnectorEntity.class,
                null,
                Collections.emptySet(),
                queryFilter,
                this.entityMapper::createConnector
        );
    }
}