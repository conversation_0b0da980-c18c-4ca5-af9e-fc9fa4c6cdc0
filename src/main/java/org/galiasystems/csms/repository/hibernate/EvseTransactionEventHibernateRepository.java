package org.galiasystems.csms.repository.hibernate;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

import org.galiasystems.csms.management.filter.QueryFilter;
import org.galiasystems.csms.management.model.EvseTransactionEvent;
import org.galiasystems.csms.management.repository.EvseTransactionEventRepository;
import org.galiasystems.csms.repository.hibernate.criteriaBuilder.CriteriaQueryHelper;
import org.galiasystems.csms.repository.hibernate.model.EvseTransactionEventEntity;
import org.galiasystems.csms.repository.hibernate.utils.HibernateRepositoryEntityMapper;

import io.quarkus.hibernate.reactive.panache.PanacheRepository;
import io.quarkus.hibernate.reactive.panache.common.WithSession;
import io.quarkus.hibernate.reactive.panache.common.WithTransaction;
import io.smallrye.mutiny.Multi;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;

@ApplicationScoped
public class EvseTransactionEventHibernateRepository implements EvseTransactionEventRepository, PanacheRepository<EvseTransactionEventEntity> {

	@Inject
	private HibernateRepositoryEntityMapper entityMapper;
	
	@Inject
    private CriteriaQueryHelper criteriaQueryHelper;
	
	@Override
	@WithTransaction
	public Uni<EvseTransactionEvent> createEvseTransactionEvent(final EvseTransactionEvent evseTransactionEvent) {
		
		final EvseTransactionEventEntity transactionEventEntity = 
		        this.entityMapper.createEvseTransactionEventEntity(evseTransactionEvent);
		return persist(transactionEventEntity)
				.onFailure()
				.invoke((error) -> {
					System.out.print(error);
				})
				.map(this.entityMapper::createEvseTransactionEvent);
	}

	@Override
	@WithSession
	public Uni<Optional<EvseTransactionEvent>> getEvseTransactionEvent(final Long id) {
	    
		return findById(id)
				.map(this.entityMapper::createEvseTransactionEvent)
				.map(Optional::ofNullable);
	}

	@Override
	@WithSession
	public Uni<List<EvseTransactionEvent>> listEvseTransactionEvents(final QueryFilter queryFilter) {
		
	    return criteriaQueryHelper.list(
	            EvseTransactionEventEntity.class,
                null,
                Collections.emptySet(),
                queryFilter,
                this.entityMapper::createEvseTransactionEventList
        );
    }
    
    @Override
    public Multi<EvseTransactionEvent> streamEvseTransactionEvents(final QueryFilter queryFilter) {
        
        return criteriaQueryHelper.stream(
                EvseTransactionEventEntity.class,
                null,
                Collections.emptySet(),
                queryFilter,
                this.entityMapper::createEvseTransactionEvent
        );
    }
}