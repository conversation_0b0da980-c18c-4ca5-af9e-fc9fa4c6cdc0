package org.galiasystems.csms.repository.hibernate;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.function.Consumer;

import org.apache.commons.lang3.function.TriConsumer;
import org.galiasystems.csms.management.filter.QueryFilter;
import org.galiasystems.csms.management.model.Session;
import org.galiasystems.csms.management.model.enums.attribute.SessionAttribute;
import org.galiasystems.csms.management.repository.SessionRepository;
import org.galiasystems.csms.management.types.TimeChartPoint;
import org.galiasystems.csms.management.types.enums.TimeChartGranularity;
import org.galiasystems.csms.repository.hibernate.criteriaBuilder.CriteriaQueryHelper;
import org.galiasystems.csms.repository.hibernate.model.SessionEntity;
import org.galiasystems.csms.repository.hibernate.model.SessionEntity_;
import org.galiasystems.csms.repository.hibernate.utils.DataBaseProviderSpecificFunctions;
import org.galiasystems.csms.repository.hibernate.utils.HibernateRepositoryEntityMapper;

import io.quarkus.hibernate.reactive.panache.PanacheRepository;
import io.quarkus.hibernate.reactive.panache.common.WithSession;
import io.quarkus.hibernate.reactive.panache.common.WithTransaction;
import io.smallrye.mutiny.Multi;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.NoResultException;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Expression;
import jakarta.persistence.criteria.Path;
import jakarta.persistence.criteria.Root;

@ApplicationScoped
public class SessionHibernateRepository extends HibernateRepositoryBase implements SessionRepository, 
                PanacheRepository<SessionEntity> {

    @Inject
    private HibernateRepositoryEntityMapper entityMapper;

    @Inject
    private CriteriaQueryHelper criteriaQueryHelper;
    
    @Inject
    private DataBaseProviderSpecificFunctions dataBaseProviderSpecificFunctions;

    @Override
	@WithTransaction
	public Uni<Session> createSession(final Session session) {
        
		final SessionEntity sessionEntity = this.entityMapper.createSessionEntity(session);
		return persist(sessionEntity)
				.map(this.entityMapper::createSession);

	}
    
    @Override
    @WithSession
    public Uni<Optional<Session>> getSession(final Long id) {
        
        return findSessionEntityById(id)
                .map(this.entityMapper::createSession)
                .map(Optional::ofNullable);
    }
    
	@Override
	@WithTransaction
	public Uni<Session> updateSession(final Long id, final Consumer<Session> changeCallback) {
		
	    return findSessionEntityById(id)
                .onItem()
                .ifNull()
                .failWith(new NoResultException("Session with id = " + id + " not found for update."))
                .map((sessionEntity) -> 
                    this.updateSessionEntity(sessionEntity, changeCallback));
	}
	
	@Override
	@WithTransaction
	public Uni<Session> updateSession(final QueryFilter queryFilter, final Consumer<Session> changeCallback) {
		
	    return this.criteriaQueryHelper.findNullable(SessionEntity.class, 
	                    null, 
	                    Collections.emptySet(),
	                    this.addDefaultFiltering(queryFilter)
	            )
	            .map((sessionEntity) -> 
                    this.updateSessionEntity(sessionEntity, changeCallback));
	}
	
	private Session updateSessionEntity(final SessionEntity sessionEntity, 
	        final Consumer<Session> changeCallback) {
	    
	    final Session session = this.entityMapper.createSession(sessionEntity);
        changeCallback.accept(session);
        this.entityMapper.updateSessionEntity(sessionEntity, session);
		return this.entityMapper.createSession(sessionEntity);
	}
	
	private Uni<SessionEntity> findSessionEntityById(final Long id) {
        
        final QueryFilter queryFilter = new QueryFilter()
                .addEqualFilter(SessionAttribute.ID, id);
        
        return criteriaQueryHelper.findNullable(
                SessionEntity.class,
                null,
                Collections.emptySet(),
                this.addDefaultFiltering(queryFilter)
        );
    }
    
	@Override
	@WithSession
	public Uni<Optional<Session>> findSession(final QueryFilter queryFilter) {
		
	    return this.criteriaQueryHelper.find(
				SessionEntity.class,
				null,
				Collections.emptySet(),
                this.addDefaultFiltering(queryFilter),
				this.entityMapper::createSession
		);
	}

    @Override
    @WithSession
    public Uni<List<Session>> listSessions(final QueryFilter queryFilter) {
        
        return this.criteriaQueryHelper.list(
                SessionEntity.class,
                null,
                Collections.emptySet(),
                this.addDefaultFiltering(queryFilter),
                this.entityMapper::createSessionList
        );
    }

    @Override
    public Multi<Session> streamSessions(final QueryFilter queryFilter) {
        
        return this.criteriaQueryHelper.stream(
                SessionEntity.class,
                null,
                Collections.emptySet(),
                this.addDefaultFiltering(queryFilter),
                this.entityMapper::createSession
        );
    }

    @Override
    @WithSession
	public Uni<Long> countSessions(final QueryFilter queryFilter) {
		
        return this.criteriaQueryHelper.count(
				SessionEntity.class,
				Collections.emptySet(),
                this.addDefaultFiltering(queryFilter)
		);
    }

    @Override
    public Multi<TimeChartPoint> getEnergySuppliedTimeChartData(final TimeChartGranularity granularity, final QueryFilter queryFilter) {
        
        final TriConsumer<CriteriaBuilder, CriteriaQuery<TimeChartPoint>, Root<SessionEntity>> criteriaQueryConfigurator =
                (cb, cq, root) -> {
                    final Expression<ZonedDateTime> truncatedDate = createTruncateDateDBFunction(granularity, cb,
                            root.get(SessionEntity_.startDateTime));

                    final Expression<BigDecimal> sumAmount = cb.sum(root.get(SessionEntity_.kwh));

                     cq.select(
                         cb.construct(TimeChartPoint.class, truncatedDate, sumAmount)
                     );
                     cq.groupBy(truncatedDate);
                     cq.orderBy(cb.asc(truncatedDate));
                };
        
        return this.criteriaQueryHelper.stream(
                SessionEntity.class,
                TimeChartPoint.class,
                null,
                Collections.emptySet(),
                this.addDefaultFiltering(queryFilter),
                Optional.of(criteriaQueryConfigurator) 
        );
    }

    private Expression<ZonedDateTime> createTruncateDateDBFunction(final TimeChartGranularity granularity, final CriteriaBuilder cb,
            final Path<?> attribute) {
        
        switch (granularity) {
            case Daily: 
                return this.dataBaseProviderSpecificFunctions.truncateDateByDayFunction(cb, attribute);
            case Monthly:
                return this.dataBaseProviderSpecificFunctions.truncateDateByMonthFunction(cb, attribute);
            default:
                throw new IllegalArgumentException("Unexpected value: " + granularity);
        }
    }
        
    
    private QueryFilter addDefaultFiltering(final QueryFilter queryFilter) {
        
        return queryFilter
            .addEqualFilter(SessionAttribute.CHARGING_STATION_ACCESS_USER_ID, this.getUserId());
    }
}