package org.galiasystems.csms.repository.hibernate;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.function.Consumer;

import org.galiasystems.csms.management.filter.QueryFilter;
import org.galiasystems.csms.management.model.PowerGrid;
import org.galiasystems.csms.management.model.enums.attribute.PowerGridAttribute;
import org.galiasystems.csms.management.repository.PowerGridRepository;
import org.galiasystems.csms.repository.hibernate.criteriaBuilder.CriteriaQueryHelper;
import org.galiasystems.csms.repository.hibernate.model.PowerGridEntity;
import org.galiasystems.csms.repository.hibernate.utils.HibernateRepositoryEntityMapper;

import io.quarkus.hibernate.reactive.panache.PanacheRepository;
import io.quarkus.hibernate.reactive.panache.common.WithSession;
import io.quarkus.hibernate.reactive.panache.common.WithTransaction;
import io.smallrye.mutiny.Multi;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.NoResultException;

@ApplicationScoped
public class PowerGridHibernateRepository extends HibernateRepositoryBase implements PowerGridRepository, 
                PanacheRepository<PowerGridEntity>  {

	@Inject
	private HibernateRepositoryEntityMapper entityMapper;
	
	@Inject
    private CriteriaQueryHelper criteriaQueryHelper;
	
	@Override
	@WithTransaction
    public Uni<PowerGrid> createPowerGrid(final PowerGrid powerGrid) {
	    
	    final PowerGridEntity powerGridEntity = this.entityMapper.createPowerGridEntity(powerGrid);
        return persist(powerGridEntity)
                .map(this.entityMapper::createPowerGrid);
    }
	
	@Override
    @WithSession
    public Uni<Optional<PowerGrid>> getPowerGrid(final Long id) {
	    
        return findPowerGridEntityById(id)
                .map(this.entityMapper::createPowerGrid)
                .map(Optional::ofNullable);
    }
	
    @Override
    @WithTransaction
    public Uni<PowerGrid> updatePowerGrid(final Long id, final Consumer<PowerGrid> changeCallback) {
        
        return findPowerGridEntityById(id)
                .onItem()
                .ifNull()
                .failWith(new NoResultException("Power grid with id = " + id + " not found for update."))
                .map((powerGridEntity) -> 
                    this.updatePowerGridEntity(powerGridEntity, changeCallback));
    }
    
    private PowerGrid updatePowerGridEntity(final PowerGridEntity powerGridEntity, 
            final Consumer<PowerGrid> changeCallback) {
        
        final PowerGrid powerGrid = this.entityMapper.createPowerGrid(powerGridEntity);
        changeCallback.accept(powerGrid);
        this.entityMapper.updatePowerGridEntity(powerGridEntity, powerGrid);
        return this.entityMapper.createPowerGrid(powerGridEntity);
    }
    
    private Uni<PowerGridEntity> findPowerGridEntityById(final Long id) {
        
        final QueryFilter queryFilter = new QueryFilter()
                .addEqualFilter(PowerGridAttribute.ID, id);
        
        return criteriaQueryHelper.findNullable(
                PowerGridEntity.class,
                null,
                Collections.emptySet(),
                this.addDefaultFiltering(queryFilter)
        );
    }

    @Override
    @WithSession
    public Uni<Optional<PowerGrid>> findPowerGrid(final QueryFilter queryFilter) {
        
        return criteriaQueryHelper.find(
                PowerGridEntity.class,
                null,
                Collections.emptySet(),
                this.addDefaultFiltering(queryFilter),
                this.entityMapper::createPowerGrid
        );
    }

    @Override
    @WithSession
    public Uni<List<PowerGrid>> listPowerGrids(final QueryFilter queryFilter) {
        
        return criteriaQueryHelper.list(
                PowerGridEntity.class,
                null,
                Collections.emptySet(),
                this.addDefaultFiltering(queryFilter),
                this.entityMapper::createPowerGridList
        );
    }
    
    @Override
    public Multi<PowerGrid> streamPowerGrids(final QueryFilter queryFilter) {
        
        return criteriaQueryHelper.stream(
                PowerGridEntity.class,
                null,
                Collections.emptySet(),
                this.addDefaultFiltering(queryFilter),
                this.entityMapper::createPowerGrid
        );
    }

    private QueryFilter addDefaultFiltering(final QueryFilter queryFilter) {
        return queryFilter
            .addEqualFilter(PowerGridAttribute.POWER_GRID_ACCESS_USER_ID, this.getUserId());
    }
}