package org.galiasystems.csms.repository.hibernate;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.function.Consumer;

import org.galiasystems.csms.management.filter.QueryFilter;
import org.galiasystems.csms.management.model.ChargingAccessToken;
import org.galiasystems.csms.management.model.enums.attribute.ChargingAccessTokenAttribute;
import org.galiasystems.csms.management.repository.ChargingAccessTokenRepository;
import org.galiasystems.csms.repository.hibernate.criteriaBuilder.CriteriaQueryHelper;
import org.galiasystems.csms.repository.hibernate.model.ChargingAccessTokenEntity;
import org.galiasystems.csms.repository.hibernate.utils.HibernateRepositoryEntityMapper;

import io.quarkus.hibernate.reactive.panache.PanacheRepository;
import io.quarkus.hibernate.reactive.panache.common.WithSession;
import io.quarkus.hibernate.reactive.panache.common.WithTransaction;
import io.smallrye.mutiny.Multi;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.NoResultException;

@ApplicationScoped
public class ChargingAccessTokenHibernateRepository extends HibernateRepositoryBase implements ChargingAccessTokenRepository, 
                PanacheRepository<ChargingAccessTokenEntity> {

    @Inject
    private HibernateRepositoryEntityMapper entityMapper;
    
    @Inject
    private CriteriaQueryHelper criteriaQueryHelper;
    
    @Override
    @WithTransaction
    public Uni<ChargingAccessToken> createChargingAccessToken(final ChargingAccessToken chargingAccessToken) {
        
        final ChargingAccessTokenEntity chargingAccessTokenEntity = 
                this.entityMapper.createChargingAccessTokenEntity(chargingAccessToken);
        return persist(chargingAccessTokenEntity)
                .map(this.entityMapper::createChargingAccessToken);
    }

    @Override
    @WithSession
    public Uni<Optional<ChargingAccessToken>> getChargingAccessToken(final Long id) {
        
        return findChargingAccessTokenEntityById(id)
                .map(this.entityMapper::createChargingAccessToken)
                .map(Optional::ofNullable);
    }

    @Override
    @WithTransaction
    public Uni<ChargingAccessToken> updateChargingAccessToken(final Long id, final Consumer<ChargingAccessToken> changeCallback) {
        
        return findChargingAccessTokenEntityById(id)
                .onItem()
                .ifNull()
                .failWith(new NoResultException("Charging access token with id = " + id + " not found for update."))
                .map((connectorEntity) -> 
                    this.updateChargingAccessTokenEntity(connectorEntity, changeCallback));
    }
    
    @Override
    @WithTransaction
    public Uni<ChargingAccessToken> updateChargingAccessToken(final QueryFilter queryFilter,
            final Consumer<ChargingAccessToken> changeCallback) {
       
        return this.criteriaQueryHelper.findNullable(ChargingAccessTokenEntity.class, null, Collections.emptySet(), 
                        this.addDefaultFiltering(queryFilter))
                .onItem()
                .ifNull()
                .failWith(new NoResultException("harging access token not found for update."))
                .map((chargingAccessTokenEntity) -> 
                    this.updateChargingAccessTokenEntity(chargingAccessTokenEntity, changeCallback));
    }
    
    private ChargingAccessToken updateChargingAccessTokenEntity(final ChargingAccessTokenEntity chargingAccessTokenEntity, 
            final Consumer<ChargingAccessToken> changeCallback) {
        
        final ChargingAccessToken chargingAccessToken = this.entityMapper.createChargingAccessToken(chargingAccessTokenEntity);
        changeCallback.accept(chargingAccessToken);
        this.entityMapper.updateChargingAccessTokenEntity(chargingAccessTokenEntity, chargingAccessToken);
        return this.entityMapper.createChargingAccessToken(chargingAccessTokenEntity);
    }
    
    private Uni<ChargingAccessTokenEntity> findChargingAccessTokenEntityById(final Long id) {
        
        final QueryFilter queryFilter = new QueryFilter()
                .addEqualFilter(ChargingAccessTokenAttribute.ID, id);
        
        return criteriaQueryHelper.findNullable(
                ChargingAccessTokenEntity.class,
                null,
                Collections.emptySet(),
                this.addDefaultFiltering(queryFilter)
        );
    }

    @Override
    @WithSession
    public Uni<Optional<ChargingAccessToken>> findChargingAccessToken(final QueryFilter queryFilter) {
        
        return criteriaQueryHelper.find(
                ChargingAccessTokenEntity.class,
                null,
                Collections.emptySet(),
                this.addDefaultFiltering(queryFilter),
                this.entityMapper::createChargingAccessToken);
    }

    @Override
    @WithSession
    public Uni<List<ChargingAccessToken>> listChargingAccessTokens(final QueryFilter queryFilter) {
        
        return criteriaQueryHelper.list(
                ChargingAccessTokenEntity.class,
                null,
                Collections.emptySet(),
                this.addDefaultFiltering(queryFilter),
                this.entityMapper::createChargingAccessTokenList
        );
    }

    @Override
    public Multi<ChargingAccessToken> streamChargingAccessTokens(final QueryFilter queryFilter) {
        
        return criteriaQueryHelper.stream(
                ChargingAccessTokenEntity.class,
                null,
                Collections.emptySet(),
                this.addDefaultFiltering(queryFilter),
                this.entityMapper::createChargingAccessToken
        );
    }
    
    private QueryFilter addDefaultFiltering(final QueryFilter queryFilter) {
        return queryFilter
            .addEqualFilter(ChargingAccessTokenAttribute.ORGANIZATION_ACCESS_USER_ID, this.getUserId());
    }
}
