package org.galiasystems.csms.repository.hibernate;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.function.Consumer;

import org.galiasystems.csms.management.filter.QueryFilter;
import org.galiasystems.csms.management.model.LocationAccess;
import org.galiasystems.csms.management.repository.LocationAccessRepository;
import org.galiasystems.csms.repository.hibernate.criteriaBuilder.CriteriaQueryHelper;
import org.galiasystems.csms.repository.hibernate.model.LocationAccessEntity;
import org.galiasystems.csms.repository.hibernate.utils.HibernateRepositoryEntityMapper;

import io.quarkus.hibernate.reactive.panache.PanacheRepository;
import io.quarkus.hibernate.reactive.panache.common.WithSession;
import io.quarkus.hibernate.reactive.panache.common.WithTransaction;
import io.smallrye.mutiny.Multi;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.NoResultException;

@ApplicationScoped
public class LocationAccessHibernateRepository implements LocationAccessRepository, 
                PanacheRepository<LocationAccessEntity> {
    
    @Inject
    private HibernateRepositoryEntityMapper entityMapper;
    
    @Inject
    private CriteriaQueryHelper criteriaQueryHelper;

    @Override
    @WithTransaction
    public Uni<LocationAccess> createLocationAccess(final LocationAccess locationAccess) {
        
        final LocationAccessEntity locationAccessEntity = 
                this.entityMapper.createLocationAccessEntity(locationAccess);
        return persist(locationAccessEntity)
                .map(this.entityMapper::createLocationAccess);
    }

    @Override
    @WithSession
    public Uni<Optional<LocationAccess>> getLocationAccess(final Long id) {
        
        return findById(id)
                .map(this.entityMapper::createLocationAccess)
                .map(Optional::ofNullable);
    }

    @Override
    @WithTransaction
    public Uni<LocationAccess> updateLocationAccess(final Long id, final Consumer<LocationAccess> changeCallback) {
        
        return findById(id)
                .onItem()
                .ifNull()
                .failWith(new NoResultException("Location access with id = " + id + " not found for update."))
                .map((locationAccessEntity) -> 
                    this.updateLocationAccessEntity(locationAccessEntity, changeCallback));
    }
    
    private LocationAccess updateLocationAccessEntity(final LocationAccessEntity locationAccessEntity, 
            final Consumer<LocationAccess> changeCallback) {
        
        final LocationAccess locationAccess = this.entityMapper.createLocationAccess(locationAccessEntity);
        changeCallback.accept(locationAccess);
        this.entityMapper.updateLocationAccessEntity(locationAccessEntity, locationAccess);
        return this.entityMapper.createLocationAccess(locationAccessEntity);
    }
    
    @Override
    @WithTransaction
    public Uni<Boolean> removeLocationAccess(final Long id) {
        
        return deleteById(id);
    }

    @Override
    @WithSession
    public Uni<Optional<LocationAccess>> findLocationAccess(final QueryFilter queryFilter) {
        
        return criteriaQueryHelper.find(
                LocationAccessEntity.class,
                null,
                Collections.emptySet(),
                queryFilter,
                this.entityMapper::createLocationAccess
        );
    }

    @Override
    @WithSession
    public Uni<List<LocationAccess>> listLocationAccesses(final QueryFilter queryFilter) {
        
        return criteriaQueryHelper.list(
                LocationAccessEntity.class,
                null,
                Collections.emptySet(),
                queryFilter,
                this.entityMapper::createLocationAccessList
        );
    }
    
    @Override
    public Multi<LocationAccess> streamLocationAccesses(final QueryFilter queryFilter) {
        
        return criteriaQueryHelper.stream(
                LocationAccessEntity.class,
                null,
                Collections.emptySet(),
                queryFilter,
                this.entityMapper::createLocationAccess
        );
    }
}
