quarkus.native.builder-image=quay.io/quarkus/ubi-quarkus-mandrel-builder-image:jdk-24

###########################
###### Common configs #####
###########################
quarkus.http.host=0.0.0.0
quarkus.http.port=8888
quarkus.http.test-port=0
quarkus.container-image.image=smart-charge-backend
quarkus.websockets-next.server.auto-ping-interval=2
#quarkus.log.file.enable=true
#quarkus.log.file.path=/home/<USER>/work/SmartCharge/smart-charge/test_log_1.log
#quarkus.log.file.level=DEBUG

## Enable for native build
#quarkus.native.enabled=true
#quarkus.package.jar.enabled=false

# CORS
quarkus.http.cors.enabled=true
quarkus.http.cors.origins=https://demo.app.smartcharge.it.com
quarkus.http.cors.methods=GET,POST,PUT,DELETE,OPTIONS
quarkus.http.cors.headers=Content-Type,Authorization,Accept,Origin
quarkus.http.cors.exposed-headers=Content-Disposition
quarkus.http.cors.access-control-max-age=24H
quarkus.http.cors.access-control-allow-credentials=true

quarkus.hibernate-orm.physical-naming-strategy=org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy

hibernate.reactive.panache.sessionOnDemandOpened=true
# GraphQL configuration
quarkus.smallrye-graphql.error-extension-fields=error-code

# Security
quarkus.http.auth.basic=true
quarkus.ssl.native=true
quarkus.http.ssl-port=8443

# TODO use tls registry: https://quarkus.io/guides/tls-registry-reference
quarkus.http.ssl.certificate.key-store-file=javaStores/server.keystore
quarkus.http.ssl.certificate.key-store-password=password

# none, request, required
quarkus.http.ssl.client-auth=request
quarkus.http.insecure-requests=enabled
quarkus.http.ssl.certificate.trust-store-file=javaStores/server.truststore
quarkus.http.ssl.certificate.trust-store-password=password
quarkus.native.additional-build-args[0]=-J-Djavax.net.ssl.trustStore=/home/<USER>/dev/smart-charge/src/main/resources/javaStores/client.truststore
quarkus.native.additional-build-args[1]=-J-Djavax.net.ssl.trustStorePassword=password
quarkus.native.additional-build-args[2]=--initialize-at-run-time=org.galiasystems.csms.utils.RandomUtil
quarkus.native.additional-build-args[3]=--initialize-at-run-time=org.galiasystems.csms.management.model.ChargingStation
quarkus.native.additional-build-args[4]=-J-Djava.awt.headless=true
quarkus.native.resources.includes=javaStores/**

# OAuth2
#quarkus.oidc-client.credentials.secret=secret
quarkus.oidc-client.grant.type=code
#quarkus.http.auth.permission.authenticated.paths=/*
#quarkus.http.auth.permission.authenticated.policy=deny
#quarkus.smallrye-jwt.enabled=true

# CSMS
csms.oauth2.registration.endpoint=http://localhost:8180/realms/flutter-app/protocol/openid-connect/registrations/
csms.oauth2.authorization.endpoint=http://localhost:8180/realms/flutter-app/protocol/openid-connect/auth/
csms.oauth2.session-id.cookie.name=SESSION_ID
csms.oauth2.access-token.cookie.name=ACCESS_TOKEN
csms.oauth2.refresh-token.cookie.name=REFRESH_TOKEN

#csms.ws.url.base=
# OpenTelemetry
quarkus.otel.enabled=true
quarkus.otel.metrics.enabled=true
quarkus.otel.instrument.jvm-metrics=true
quarkus.otel.instrument.http-server-metrics=true

# OTLP Exporter configuration
quarkus.otel.exporter.otlp.endpoint=http://localhost:4317

# OCPP Metrics - rolling window duration in seconds for time-based metrics
ocpp.metrics.rolling-window-seconds=60

###########################
###### Dev profile ########
###########################
# Datasource configs
%dev.quarkus.datasource.devservices.port=53277

#dev.quarkus.datasource.db-kind=postgresql
#dev.quarkus.datasource.username=smart_charge
#dev.quarkus.datasource.password=smart_charge
#dev.quarkus.datasource.jdbc.url=*********************************************
#dev.quarkus.datasource.reactive.url=vertx-reactive:postgresql://localhost:5432/smart_charge?currentSchema=smart_charge
#dev.quarkus.datasource.reactive.max-size=20

%dev.quarkus.log.category."org.galiasystems".level=DEBUG
# %dev.quarkus.log.level=DEBUG
# %dev.quarkus.http.access-log.enabled=true

# CORS
%dev.quarkus.http.cors.origins=/.*/

# Hibernate configs
%dev.quarkus.hibernate-orm.log.sql=true
#%dev.quarkus.hibernate-orm.schema-management.strategy=drop-and-create
%dev.quarkus.hibernate-orm.schema-management.strategy=none
%dev.quarkus.hibernate-orm.sql-load-script=no-file

# Flyway config
%dev.quarkus.flyway.enabled=true
%dev.quarkus.flyway.migrate-at-start=true
%dev.quarkus.flyway.clean-at-start=true
#dev.quarkus.flyway.baseline-on-migrate=true
#dev.quarkus.flyway.baseline-version=0

# OAuth2
%dev.quarkus.oidc-client.auth-server-url=http://localhost:8180/realms/flutter-app/
%dev.quarkus.oidc-client.client-id=flutter-client
%dev.quarkus.oidc-client.credentials.secret=63vMfP5b0xa8Pjx6KbNquYqggP6Z76jC
# secret on oci environment
#%dev.quarkus.oidc-client.credentials.secret=roYrOkcqmMJscLp8BeqeiGHdvw8Hp7o1
#%dev.quarkus.oidc.auth-server-url=http://localhost:8180/realms/flutter-app/
%dev.quarkus.rest-client.custom-keycloak.url=http://localhost:8180/realms/flutter-app
%dev.quarkus.keycloak.devservices.enabled=true

# Mock OidcClient
#%dev.quarkus.arc.selected-alternatives=org.galiasystems.csms.security.login.oauth2.MockOidcClient

%dev.quarkus.smallrye-graphql.error-extension-fields=error-code,exception-details

###########################
###### Test profile #######
###########################
# Datasource configs
%test.quarkus.datasource.devservices.port=53277
%test.quarkus.http.host=0.0.0.0
%test.quarkus.http.port=8081

# CORS
%test.quarkus.http.cors.origins=http://localhost:8888

# Hibernate configs
%test.quarkus.hibernate-orm.log.sql=true
%test.quarkus.hibernate-orm.schema-management.strategy=none
%test.quarkus.hibernate-orm.sql-load-script=no-file

# Mock OidcClient
%test.quarkus.arc.selected-alternatives=org.galiasystems.csms.security.login.oauth2.MockOidcClient

# OAuth2
%test.quarkus.rest-client.custom-keycloak.url=http://localhost:8180/realms/flutter-app

# Flyway config
%test.quarkus.flyway.enabled=true
%test.quarkus.flyway.clean-at-start=true
%test.quarkus.flyway.migrate-at-start=true

%test.quarkus.smallrye-graphql.error-extension-fields=error-code,exception-details

###########################
###### Prod profile #######
###########################
%prod.quarkus.datasource.db-kind=postgresql
%prod.quarkus.datasource.username=smart_charge
%prod.quarkus.datasource.password=smart_charge
%prod.quarkus.log.level=DEBUG

# Reactive config
%prod.quarkus.datasource.reactive.url=vertx-reactive:postgresql://postgres/smart_charge?currentSchema=smart_charge
%prod.quarkus.datasource.reactive.max-size=20

# Flyway config
%prod.quarkus.flyway.enabled=true
%prod.quarkus.flyway.migrate-at-start=true

# JDBC config
%prod.quarkus.datasource.jdbc.url=******************************************************************

%prod.quarkus.hibernate-orm.schema-management.strategy = none
%prod.quarkus.hibernate-orm.sql-load-script = no-file

# CORS
%prod.quarkus.http.cors.origins=https://demo.app.smartcharge.it.com

# OAuth2
%prod.quarkus.oidc-client.auth-server-url=${QUARKUS_OIDC_CLIENT_AUTH_SERVER_URL}
%prod.quarkus.oidc-client.client-id=flutter-client
%prod.quarkus.oidc-client.credentials.secret=${QUARKUS_OIDC_CLIENT_CREDENTIALS_SECRET}
%prod.quarkus.rest-client.custom-keycloak.url=${QUARKUS_REST_CLIENT_CUSTOM_KEYCLOAK_URL}



###########################
###### Samples      #######
###########################


# Run Flyway migrations automatically
# quarkus.flyway.migrate-at-start=true

#manually by injecting the Flyway object and calling Flyway#repair().
# quarkus.flyway.repair-at-start=true

# More Flyway configuration options
# quarkus.flyway.baseline-on-migrate=true
# quarkus.flyway.baseline-version=1.0.0
# quarkus.flyway.baseline-description=Initial version
# quarkus.flyway.connect-retries=10
# quarkus.flyway.schemas=TEST_SCHEMA
# quarkus.flyway.table=flyway_quarkus_history
# quarkus.flyway.locations=db/location1,db/location2
# quarkus.flyway.sql-migration-prefix=X
# quarkus.flyway.repeatable-sql-migration-prefix=K
# %dev.quarkus.flyway.clean-at-start=true

# %dev-with-data.quarkus.hibernate-orm.schema-management.strategy = update
# %dev-with-data.quarkus.hibernate-orm.sql-load-script = no-file