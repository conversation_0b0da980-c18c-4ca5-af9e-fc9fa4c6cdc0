-- Create all sequences first
CREATE SEQUENCE IF NOT EXISTS tenant_id_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE IF NOT EXISTS location_id_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE IF NOT EXISTS organization_id_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE IF NOT EXISTS floor_id_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE IF NOT EXISTS power_grid_id_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE IF NOT EXISTS charging_station_id_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE IF NOT EXISTS evse_id_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE IF NOT EXISTS connector_id_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE IF NOT EXISTS evse_transaction_id_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE IF NOT EXISTS evse_transaction_event_id_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE IF NOT EXISTS charging_station_variable_id_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE IF NOT EXISTS charging_station_variable_value_id_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE IF NOT EXISTS report_id_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE IF NOT EXISTS meter_value_id_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE IF NOT EXISTS csms_user_id_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE IF NOT EXISTS session_id_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE IF NOT EXISTS transaction_id_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE IF NOT EXISTS authorization_code_challenge_id_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE IF NOT EXISTS csms_user_tokens_id_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE IF NOT EXISTS access_group_id_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE IF NOT EXISTS organization_access_id_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE IF NOT EXISTS location_access_id_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE IF NOT EXISTS power_grid_access_id_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE IF NOT EXISTS charging_station_access_id_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE IF NOT EXISTS charging_access_token_id_seq START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE IF NOT EXISTS charging_access_token_history_id_seq START WITH 1 INCREMENT BY 1;

-- Create tenant table
CREATE TABLE IF NOT EXISTS tenant (
    id BIGINT PRIMARY KEY DEFAULT nextval('tenant_id_seq'),
    name VARCHAR(255) NOT NULL UNIQUE,
    version INTEGER NOT NULL DEFAULT 0
);

-- Create organization table
CREATE TABLE IF NOT EXISTS organization (
    id BIGINT PRIMARY KEY DEFAULT nextval('organization_id_seq'),
    name VARCHAR(255) NOT NULL,
    licence_type VARCHAR(50) NOT NULL,
    create_date_time TIMESTAMP WITH TIME ZONE NOT NULL,
    tenant_id BIGINT,
    version INTEGER NOT NULL DEFAULT 0
);

-- Create location table
CREATE TABLE IF NOT EXISTS location (
    id BIGINT PRIMARY KEY DEFAULT nextval('location_id_seq'),
    name VARCHAR(255) NOT NULL,
    address JSONB,
    organization_id BIGINT,
    version INTEGER NOT NULL DEFAULT 0
);

-- Create floor table
CREATE TABLE IF NOT EXISTS floor (
    id BIGINT PRIMARY KEY DEFAULT nextval('floor_id_seq'),
    level INTEGER NOT NULL,
    name VARCHAR(255),
    layout VARCHAR(50),
    row_count INTEGER NOT NULL,
    column_count INTEGER NOT NULL,
    location_id BIGINT NOT NULL,
    version INTEGER NOT NULL DEFAULT 0
);

-- Create power_grid table
CREATE TABLE IF NOT EXISTS power_grid (
    id BIGINT PRIMARY KEY DEFAULT nextval('power_grid_id_seq'),
    name VARCHAR(255) NOT NULL,
    max_charging_rate DECIMAL,
    min_charging_station_charging_rate DECIMAL,
    charging_rate_unit VARCHAR(50),
    profile_type VARCHAR(50) NOT NULL,
    location_id BIGINT NOT NULL,
    version INTEGER NOT NULL DEFAULT 0
);

-- Create charging_station table
CREATE TABLE charging_station (
    id BIGINT PRIMARY KEY DEFAULT nextval('charging_station_id_seq'),
    name VARCHAR(255),
    max_charging_rate DECIMAL,
    charging_rate_unit VARCHAR(50),
    status VARCHAR(50),
    connection_status VARCHAR(50),
    last_status_update TIMESTAMP WITH TIME ZONE,
    error_code VARCHAR(255),
    error_info VARCHAR(255),
    vendor_implementation_id VARCHAR(255),
    vendor_error_code VARCHAR(255),
    charge_point_serial_number VARCHAR(255),
    charge_point_vendor VARCHAR(255),
    meter_type VARCHAR(255),
    meter_serial_number VARCHAR(255),
    charge_point_model VARCHAR(255),
    iccid VARCHAR(255),
    charge_box_serial_number VARCHAR(255),
    firmware_version VARCHAR(255),
    imsi VARCHAR(255),
    adapter_type VARCHAR(50),
    user_id BIGINT,
    power_grid_id BIGINT NOT NULL,
    version INTEGER NOT NULL DEFAULT 0
);

-- Create evse table
CREATE TABLE IF NOT EXISTS evse (
    id BIGINT PRIMARY KEY DEFAULT nextval('evse_id_seq'),
    name VARCHAR(255),
    charging_station_evse_id INTEGER NOT NULL,
    status VARCHAR(50),
    parking_floor INTEGER,
    parking_row INTEGER,
    parking_column INTEGER,
    parking_place_id VARCHAR(255),
    charging_station_id BIGINT NOT NULL,
    version INTEGER NOT NULL DEFAULT 0
);

-- Create connector table
CREATE TABLE IF NOT EXISTS connector (
    id BIGINT PRIMARY KEY DEFAULT nextval('connector_id_seq'),
    evse_connector_id INTEGER NOT NULL,
    connector_type VARCHAR(50),
    status VARCHAR(50) NOT NULL,
    last_status_update TIMESTAMP WITH TIME ZONE,
    error_code VARCHAR(255),
    error_info VARCHAR(255),
    vendor_implementation_id VARCHAR(255),
    vendor_error_code VARCHAR(255),
    evse_id BIGINT NOT NULL,
    version INTEGER NOT NULL DEFAULT 0
);

-- Create evse_transaction table
CREATE TABLE IF NOT EXISTS evse_transaction (
    id BIGINT PRIMARY KEY DEFAULT nextval('evse_transaction_id_seq'),
    evse_transaction_id VARCHAR(255) NOT NULL,
    energy_active_import_register_start DECIMAL NOT NULL,
	energy_active_import_register_end DECIMAL,
    status VARCHAR(50) NOT NULL, 
    charging_rate_limit DECIMAL,
    charging_rate_unit VARCHAR(50),
    evse_id BIGINT NOT NULL,
    connector_id BIGINT,
    version INTEGER NOT NULL DEFAULT 0
);

-- Create evse_transaction_event table
CREATE TABLE IF NOT EXISTS evse_transaction_event (
    id BIGINT PRIMARY KEY DEFAULT nextval('evse_transaction_event_id_seq'),
    event_type VARCHAR(50) NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    trigger_reason VARCHAR(50) NOT NULL,
    event_seq_no INTEGER NOT NULL,
    offline BOOLEAN,
    number_of_phases_used INTEGER,
    cable_max_current INTEGER,
    reservation_id BIGINT,
    charging_state VARCHAR(50),
    time_spent_charging INTEGER,
    stopped_reason VARCHAR(50),
    remote_start_id INTEGER,
    id_token JSONB,
    meter_values JSONB NOT NULL,
    transaction_id BIGINT NOT NULL,
    version INTEGER NOT NULL DEFAULT 0
);

-- Create charging_station_variable table
CREATE TABLE IF NOT EXISTS charging_station_variable (
    id BIGINT PRIMARY KEY DEFAULT nextval('charging_station_variable_id_seq'),
    component_name VARCHAR(255),
    component_instance VARCHAR(255),
    charging_station_evse_id INTEGER,
    evse_connector_id INTEGER,
    variable_name VARCHAR(255),
    variable_instance VARCHAR(255),
    unit VARCHAR(255),
    data_type VARCHAR(50),
    min_limit DECIMAL,
    max_limit DECIMAL,
    values_list VARCHAR(255),
    supports_monitoring BOOLEAN,
    charging_station_id BIGINT NOT NULL,
    version INTEGER NOT NULL DEFAULT 0
);

-- Create charging_station_variable_value table
CREATE TABLE IF NOT EXISTS charging_station_variable_value (
    id BIGINT PRIMARY KEY DEFAULT nextval('charging_station_variable_value_id_seq'),
    type VARCHAR(50),
    value VARCHAR(255),
    mutability VARCHAR(50),
    charging_station_variable_id BIGINT NOT NULL,
    version INTEGER NOT NULL DEFAULT 0
);

-- Create report table
CREATE TABLE IF NOT EXISTS report (
    id BIGINT PRIMARY KEY DEFAULT nextval('report_id_seq'),
    type VARCHAR(50) NOT NULL,
    status VARCHAR(50),
    response_status VARCHAR(50),
    response_reason_code VARCHAR(255),
    response_additional_info VARCHAR(255),
    charging_station_id BIGINT NOT NULL,
    version INTEGER NOT NULL DEFAULT 0
);

-- Create meter_value table
CREATE TABLE IF NOT EXISTS meter_value (
    id BIGINT PRIMARY KEY DEFAULT nextval('meter_value_id_seq'),
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    value DECIMAL NOT NULL,
    context VARCHAR(50) NOT NULL,
    measurand VARCHAR(50) NOT NULL,
    phase VARCHAR(50),
    location VARCHAR(50) NOT NULL,
    signed_meter_value JSONB,
    unit_of_measure JSONB NOT NULL, 
    charging_station_id BIGINT NOT NULL,
    charging_station_evse_id INTEGER,
    version INTEGER NOT NULL DEFAULT 0
);

-- Create csms_user table
CREATE TABLE IF NOT EXISTS csms_user (
    id BIGINT PRIMARY KEY DEFAULT nextval('csms_user_id_seq'),
    user_type VARCHAR(50) NOT NULL,
    user_name VARCHAR(255) NOT NULL,
    password VARCHAR(255),
    tenant_id BIGINT NOT NULL,
    selected_organization_id BIGINT,
    version INTEGER NOT NULL DEFAULT 0,
    UNIQUE(user_name)
);

-- Create csms_user_role table
CREATE TABLE IF NOT EXISTS csms_user_role (
    role VARCHAR(50) PRIMARY KEY,
    version INTEGER NOT NULL DEFAULT 0
);

-- Create csms_user_csms_user_role table
CREATE TABLE IF NOT EXISTS csms_user_csms_user_role (
	user_id BIGINT NOT NULL,
    role VARCHAR(50) NOT NULL,
    PRIMARY KEY(user_id, role)
);    

-- Create csms_user_tokens table
CREATE TABLE IF NOT EXISTS csms_user_tokens (
    id BIGINT PRIMARY KEY DEFAULT nextval('csms_user_tokens_id_seq'),
    user_name VARCHAR(255) NOT NULL UNIQUE,
    access_token VARCHAR(255) NOT NULL UNIQUE,
    access_token_expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    refresh_token VARCHAR(255) NOT NULL UNIQUE,
    refresh_token_expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    auth_server_access_token VARCHAR(2048) NOT NULL,
    auth_server_access_token_expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    auth_server_refresh_token VARCHAR(2048) NOT NULL,
    auth_server_refresh_token_time_skew BIGINT,
    auth_server_id_token VARCHAR(2048) NOT NULL,
    user_id BIGINT NOT NULL UNIQUE,
    version INTEGER NOT NULL DEFAULT 0
);

-- Create charging_access_token table
CREATE TABLE IF NOT EXISTS charging_access_token (
    id BIGINT PRIMARY KEY DEFAULT nextval('charging_access_token_id_seq'),
    country_code VARCHAR(2) NOT NULL,
    party_id VARCHAR(255) NOT NULL,
    token VARCHAR(255) NOT NULL,
    token_type VARCHAR(255) NOT NULL,
    valid_from TIMESTAMP WITH TIME ZONE NOT NULL,
    valid_to TIMESTAMP WITH TIME ZONE,
    last_updated TIMESTAMP WITH TIME ZONE NOT NULL,
    token_owner_id BIGINT,
    organization_id BIGINT,
    version INTEGER NOT NULL DEFAULT 0,
    UNIQUE(token, token_type, organization_id)
);

CREATE TABLE IF NOT EXISTS charging_access_token_history (
    id BIGINT PRIMARY KEY DEFAULT nextval('charging_access_token_history_id_seq'),
    history_ts TIMESTAMP WITH TIME ZONE NOT NULL,
    change_by_user_name VARCHAR(255) NOT NULL,
    valid_from TIMESTAMP WITH TIME ZONE NOT NULL,
    valid_to TIMESTAMP WITH TIME ZONE,
    last_updated TIMESTAMP WITH TIME ZONE NOT NULL,
    token_owner_user_name VARCHAR(255),
    charging_access_token_id BIGINT NOT NULL,
    version INTEGER NOT NULL DEFAULT 0
);

-- Create session table
CREATE TABLE IF NOT EXISTS session (
    id BIGINT PRIMARY KEY DEFAULT nextval('session_id_seq'),
    country_code VARCHAR(2) NOT NULL,
    party_id VARCHAR(50) NOT NULL,
    start_session_response_url VARCHAR(255),
    stop_session_response_url VARCHAR(255),
    unlock_connector_response_url VARCHAR(255),
    create_date_time TIMESTAMP WITH TIME ZONE NOT NULL,
    start_date_time TIMESTAMP WITH TIME ZONE NOT NULL,
    end_date_time TIMESTAMP WITH TIME ZONE,
    kwh DECIMAL NOT NULL,
    session_token JSONB NOT NULL,    
    auth_method VARCHAR(50) NOT NULL,
    authorization_reference VARCHAR(255),
    charging_station_id BIGINT NOT NULL,
    evse_Id BIGINT,
    evse_connector_id INTEGER,
    meter_id VARCHAR(255),
    currency VARCHAR(3) NOT NULL,
    charging_periods JSONB NOT NULL,
    total_cost JSONB,
    request_start_transaction_status VARCHAR(50),
    request_stop_transaction_status VARCHAR(50),
    request_unlock_connector_status VARCHAR(50),
    status VARCHAR(50) NOT NULL,
    last_updated TIMESTAMP WITH TIME ZONE NOT NULL,
    version INTEGER NOT NULL DEFAULT 0,
    session_owner_id BIGINT NOT NULL,
    evse_transaction_id BIGINT
);

-- Create authorization_code_challenge table
CREATE TABLE IF NOT EXISTS authorization_code_challenge (
    id BIGINT PRIMARY KEY DEFAULT nextval('authorization_code_challenge_id_seq'),
    session_id VARCHAR(255) NOT NULL UNIQUE,
    code_verifier VARCHAR(255) NOT NULL,
    create_ts TIMESTAMP WITH TIME ZONE NOT NULL,
    version INTEGER NOT NULL DEFAULT 0
);

-- Create access_group table
CREATE TABLE IF NOT EXISTS access_group (
    id BIGINT PRIMARY KEY DEFAULT nextval('access_group_id_seq'),
    name VARCHAR(255) NOT NULL,
    role VARCHAR(50) NOT NULL,
    organization_id BIGINT NOT NULL,
    version INTEGER NOT NULL DEFAULT 0
);

-- Create access_group_location table
CREATE TABLE IF NOT EXISTS access_group_location (
    access_group_id BIGINT NOT NULL,
    location_id BIGINT NOT NULL,
    PRIMARY KEY(access_group_id, location_id)
);

-- Create access_group_power_grid table
CREATE TABLE IF NOT EXISTS access_group_power_grid (
    access_group_id BIGINT NOT NULL,
    power_grid_id BIGINT NOT NULL,
    PRIMARY KEY(access_group_id, power_grid_id)
);

-- Create access_group_charging_station table
CREATE TABLE IF NOT EXISTS access_group_charging_station (
    access_group_id BIGINT NOT NULL,
    charging_station_id BIGINT NOT NULL,
    PRIMARY KEY(access_group_id, charging_station_id)
);

-- Create access_group_csms_user table
CREATE TABLE IF NOT EXISTS access_group_csms_user (
    access_group_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    PRIMARY KEY(access_group_id, user_id)
);

-- Create organization_access table
CREATE TABLE IF NOT EXISTS organization_access (
    id BIGINT PRIMARY KEY DEFAULT nextval('organization_access_id_seq'),
    role VARCHAR(50) NOT NULL,
    user_id BIGINT NOT NULL,
    organization_id BIGINT NOT NULL,
    version INTEGER NOT NULL DEFAULT 0
);

-- Create location_access table
CREATE TABLE IF NOT EXISTS location_access (
    id BIGINT PRIMARY KEY DEFAULT nextval('location_access_id_seq'),
    role VARCHAR(50) NOT NULL,
    inherited BOOLEAN NOT NULL DEFAULT false,
    user_id BIGINT NOT NULL,
    location_id BIGINT NOT NULL,
    version INTEGER NOT NULL DEFAULT 0
);

-- Create power_grid_access table
CREATE TABLE IF NOT EXISTS power_grid_access (
    id BIGINT PRIMARY KEY DEFAULT nextval('power_grid_access_id_seq'),
    role VARCHAR(50) NOT NULL,
    inherited BOOLEAN NOT NULL DEFAULT false,
    user_id BIGINT NOT NULL,
    power_grid_id BIGINT NOT NULL,
    version INTEGER NOT NULL DEFAULT 0
);

-- Create charging_station_access table
CREATE TABLE IF NOT EXISTS charging_station_access (
    id BIGINT PRIMARY KEY DEFAULT nextval('charging_station_access_id_seq'),
    role VARCHAR(50) NOT NULL,
    inherited BOOLEAN NOT NULL DEFAULT false,
    user_id BIGINT NOT NULL,
    charging_station_id BIGINT NOT NULL,
    version INTEGER NOT NULL DEFAULT 0
);