-- Insert initial data (moved from import.sql)

-- Insert tenant data
insert into tenant(id, name, version) values (-1, 'DefaultTenant', 0);

-- Insert organization data
insert into organization(id, tenant_id, name, licence_type, create_date_time, version) values (-1, -1, 'eTaxi', 'Unlimited', '2025-11-07 14:00:00+00', 0);

-- Insert location data
insert into location(id, organization_id, name, address, version) values (-1, -1, 'eTaxi Budapest', 
	'{"country": "HU", "city": "Budapest", "postalCode": "1027", "street": "Csalogány utca", "houseNumber": "23"}', 0);
insert into location(id, organization_id, name, address, version) values (-2, -1, 'eTaxi Debrecen', null, 0);
insert into location(id, organization_id, name, address, version) values (-3, -1, 'eTaxi Szeged', null, 0);


-- Insert power grid data
insert into power_grid(id, location_id, name, max_charging_rate, charging_rate_unit, profile_type, version) values (-1, -1, '<PERSON>ar<PERSON>zs szint -1', 48, 'A', 'Fairness', 0);
insert into power_grid(id, location_id, name, max_charging_rate, charging_rate_unit, profile_type, version) values (-2, -1, 'Garázs szint -2', 32, 'A', 'Fairness', 0);
insert into power_grid(id, location_id, name, max_charging_rate, charging_rate_unit, profile_type, version) values (-3, -1, 'Garázs szint -3', 32, 'A', 'Fairness', 0);
insert into power_grid(id, location_id, name, max_charging_rate, charging_rate_unit, profile_type, version) values (-4, -2, 'Parkoló A', 16, 'A', 'Fairness', 0);
insert into power_grid(id, location_id, name, max_charging_rate, charging_rate_unit, profile_type, version) values (-5, -2, 'Parkoló B', 48, 'A', 'Fairness', 0);
insert into power_grid(id, location_id, name, max_charging_rate, charging_rate_unit, profile_type, version) values (-6, -3, 'Főépület', 48, 'A', 'Fairness', 0);
insert into power_grid(id, location_id, name, max_charging_rate, charging_rate_unit, profile_type, version) values (-7, -3, 'B épület', 32, 'A', 'Fairness', 0);

-- Insert role data
insert into csms_user_role(role, version) values ('Authenticated', 0);
insert into csms_user_role(role, version) values ('Maintainer', 0);

-- Insert charging station user data
insert into csms_user(id, user_type, user_name, password, tenant_id, selected_organization_id, version) values (-1, 'CS', '27e1994f-e667-46cc-98f8-29641e063e81', '$2a$10$P412KssO7zvMkaK7ZuIKQO0xqhm16ljL.vaLS8IkYLCzxS3/xm7g6', -1, null, 0);
insert into csms_user_csms_user_role(user_id, role) values (-1, 'Authenticated');
insert into organization_access(id, role, user_id, organization_id, version) values (-1, 'User', -1, -1, 0);

insert into csms_user(id, user_type, user_name, password, tenant_id, selected_organization_id, version) values (-2, 'CS', '88e59a10-3cf7-4475-bcf1-3000796de943', '$2a$10$P412KssO7zvMkaK7ZuIKQO0xqhm16ljL.vaLS8IkYLCzxS3/xm7g6', -1, null, 0);
insert into csms_user_csms_user_role(user_id, role) values (-2, 'Authenticated');
insert into organization_access(id, role, user_id, organization_id, version) values (-2, 'User', -2, -1, 0);

insert into csms_user(id, user_type, user_name, password, tenant_id, selected_organization_id, version) values (-3, 'CS', '5d7b9c4e-6692-41c8-b3dc-43c18607e72e', '$2a$10$P412KssO7zvMkaK7ZuIKQO0xqhm16ljL.vaLS8IkYLCzxS3/xm7g6', -1, null, 0);
insert into csms_user_csms_user_role(user_id, role) values (-3, 'Authenticated');
insert into organization_access(id, role, user_id, organization_id, version) values (-3, 'User', -3, -1, 0);

insert into csms_user(id, user_type, user_name, password, tenant_id, selected_organization_id, version) values (-4, 'CS', '6f28e589-2c08-4c4f-aa16-8d91629e48d0', '$2a$10$P412KssO7zvMkaK7ZuIKQO0xqhm16ljL.vaLS8IkYLCzxS3/xm7g6', -1, null, 0);
insert into csms_user_csms_user_role(user_id, role) values (-4, 'Authenticated');
insert into organization_access(id, role, user_id, organization_id, version) values (-4, 'User', -4, -1, 0);


-- Insert charging station data
insert into charging_station(id, user_id, power_grid_id, name, status, connection_status, version, max_charging_rate, charging_rate_unit)
values (-1, -1, -1, 'Wallbox Pulsar Plus 1', 'Available', 'Offline', 0, 24, 'A');
insert into charging_station_access(id, role, inherited, user_id, charging_station_id, version) values (-1, 'ChargingStation', false, -1, -1, 0);

insert into charging_station(id, user_id, power_grid_id, name, status, connection_status, version, max_charging_rate, charging_rate_unit)
values (-2, -2, -1, 'Wallbox Pulsar Plus 2', 'Online', 'Offline', 0, 24, 'A');
insert into charging_station_access(id, role, inherited, user_id, charging_station_id, version) values (-2, 'ChargingStation', false, -2, -2, 0);

insert into charging_station(id, user_id, power_grid_id, name, status, connection_status, version, max_charging_rate, charging_rate_unit)
values (-3, -3, -1, 'V2C Trydan Pro', 'Available', 'Connected', 0, 24, 'A');
insert into charging_station_access(id, role, inherited, user_id, charging_station_id, version) values (-3, 'ChargingStation', false, -3, -3, 0);

insert into charging_station(id, user_id, power_grid_id, name, status, connection_status, version, max_charging_rate, charging_rate_unit)
values (-4, -4, -1, 'Rheidon Wallbox', 'Faulted', 'Connected', 0, 24, 'A');
insert into charging_station_access(id, role, inherited, user_id, charging_station_id, version) values (-4, 'ChargingStation', false, -4, -4, 0);

-- Insert EVSE data
insert into evse(id, charging_station_id, name, charging_station_evse_id, status, parking_floor, parking_row, parking_column, parking_place_id, version) 
values (-1, -1, 'Wallbox Pulsar Plus 1', 1, 'Available', null, null, null, null, 0);
insert into evse(id, charging_station_id, name, charging_station_evse_id, status, parking_floor, parking_row, parking_column, parking_place_id, version)
values (-2, -2, 'Wallbox Pulsar Plus 2 A', 1, 'Available', null, null, null, null, 0);
insert into evse(id, charging_station_id, name, charging_station_evse_id, status, parking_floor, parking_row, parking_column, parking_place_id, version)
values (-3, -2, 'Wallbox Pulsar Plus 2 B', 2, 'Available', null, null, null, null, 0);

-- Insert connector data
insert into connector(id, evse_id, evse_connector_id, connector_type, status, version) 
values (-1, -1, 1, 'DC_CCS2', 'Available', 0);
--insert into connector(id, evse_id, evse_connector_id, connector_type, status, version)
--values (-2, -1, 2, 'AC_Mennekes_Type2', 'Available', 0);
insert into connector(id, evse_id, evse_connector_id, connector_type, status, version)
values (-3, -2, 1, 'DC_CCS2', 'Available', 0);
insert into connector(id, evse_id, evse_connector_id, connector_type, status, version)
values (-4, -3, 1, null, 'Available', 0);

-- Insert EVSE transaction data
insert into evse_transaction(id, evse_id, connector_id, evse_transaction_id, energy_active_import_register_start, energy_active_import_register_end, status, version) 
values (-1, -1, -1, '-1', 10.2, 20.5, 'Finished', 0);
--insert into evse_transaction(id, evse_id, connector_id, evse_transaction_id, energy_active_import_register_start, energy_active_import_register_end, status, version)
--values (-2, -2, -2, '-2', 20.5, 46.9, 'Finished', 0);
insert into evse_transaction(id, evse_id, connector_id, evse_transaction_id, energy_active_import_register_start, energy_active_import_register_end, status, version)
values (-3, -3, -3, '-3', 46.9, 79.5, 'Finished', 0);
insert into evse_transaction(id, evse_id, connector_id, evse_transaction_id, energy_active_import_register_start, energy_active_import_register_end, status, version)
values (-4, -3, -3, '-4', 79.5, 103.7, 'Finished', 0);

-- Insert charging station variable data
insert into charging_station_variable(id, charging_station_id, component_name, component_instance, charging_station_evse_id, evse_connector_id, variable_name, variable_instance, unit, data_type, min_limit, max_limit, values_list, supports_monitoring, version) 
values (-1, -1, 'component_name', 'component_instance', 1, 1, 'variable_name_full', 'variable_instance', 'unit', 'String', 1, 10, 'values_list', true, 0);

insert into charging_station_variable_value(id, charging_station_variable_id, type, value, mutability, version) 
values (-1, -1, 'Actual', 'actual value', 'ReadWrite', 0);

insert into charging_station_variable_value(id, charging_station_variable_id, type, value, mutability, version) 
values (-2, -1, 'Target', 'target value', 'WriteOnly', 0);

insert into charging_station_variable(id, charging_station_id, component_name, component_instance, charging_station_evse_id, evse_connector_id, variable_name, variable_instance, unit, data_type, min_limit, max_limit, values_list, supports_monitoring, version) 
values (-2, -1, 'component_name', null, null, null, 'variable_name_min', null, null, null, null, null, null, null, 0);

insert into charging_station_variable_value(id, charging_station_variable_id, type, value, mutability, version) 
values (-5, -2, null, null, null, 0);

-- Insert CSMS user data
insert into csms_user(id, user_type, user_name, password, tenant_id, selected_organization_id, version) values (-5, 'CSMS', '<EMAIL>', null, -1, -1, 0);
insert into charging_access_token(id, country_code, party_id, token, token_type, valid_from, valid_to, last_updated, token_owner_id, version) values (-1, 'HU', 'SMART_CHARGE', 'd6b1bfc1-22f8-4ded-9108-04c35a603d5b', 'APP_USER', '2025-04-01 07:00:00+00', null, '2025-04-01 07:00:00+00', -5, 0);
insert into charging_access_token_history(id, history_ts, change_by_user_name, valid_from, valid_to, last_updated, token_owner_user_name, charging_access_token_id, version) values (-1, '2025-04-01 07:00:00+00', '<EMAIL>', '2025-04-01 07:00:00+00', null, '2025-04-01 07:00:00+00', '<EMAIL>', -1, 0);
insert into csms_user_csms_user_role(user_id, role) values (-5, 'Authenticated');
insert into organization_access(id, role, user_id, organization_id, version) values (-5, 'Owner', -5, -1, 0);
insert into location_access(id, role, inherited, user_id, location_id, version) values (-1, 'Admin', true, -5, -1, 0);
insert into location_access(id, role, inherited, user_id, location_id, version) values (-2, 'Admin', true, -5, -2, 0);
insert into location_access(id, role, inherited, user_id, location_id, version) values (-3, 'Admin', true, -5, -3, 0);
insert into power_grid_access(id, role, inherited, user_id, power_grid_id, version) values (-1, 'Admin', true, -5, -1, 0);
insert into power_grid_access(id, role, inherited, user_id, power_grid_id, version) values (-2, 'Admin', true, -5, -2, 0);
insert into power_grid_access(id, role, inherited, user_id, power_grid_id, version) values (-3, 'Admin', true, -5, -3, 0);
insert into power_grid_access(id, role, inherited, user_id, power_grid_id, version) values (-4, 'Admin', true, -5, -4, 0);
insert into power_grid_access(id, role, inherited, user_id, power_grid_id, version) values (-5, 'Admin', true, -5, -5, 0);
insert into power_grid_access(id, role, inherited, user_id, power_grid_id, version) values (-6, 'Admin', true, -5, -6, 0);
insert into power_grid_access(id, role, inherited, user_id, power_grid_id, version) values (-7, 'Admin', true, -5, -7, 0);
insert into charging_station_access(id, role, inherited, user_id, charging_station_id, version) values (-5, 'Admin', true, -5, -1, 0);
insert into charging_station_access(id, role, inherited, user_id, charging_station_id, version) values (-6, 'Admin', true, -5, -2, 0);
insert into charging_station_access(id, role, inherited, user_id, charging_station_id, version) values (-7, 'Admin', true, -5, -3, 0);
insert into charging_station_access(id, role, inherited, user_id, charging_station_id, version) values (-8, 'Admin', true, -5, -4, 0);

insert into csms_user(id, user_type, user_name, password, tenant_id, selected_organization_id, version) values (-6, 'CSMS', '<EMAIL>', null, -1, -1, 0);
insert into charging_access_token(id, country_code, party_id, token, token_type, valid_from, valid_to, last_updated, token_owner_id, version) values (-2, 'HU', 'SMART_CHARGE', '4a631a94-2c62-4af9-84ce-26dba5aad2ce', 'APP_USER', '2025-09-11 06:30:00+00', null, '2025-09-11 06:30:00+00', -6, 0);
insert into charging_access_token_history(id, history_ts, change_by_user_name, valid_from, valid_to, last_updated, token_owner_user_name, charging_access_token_id, version) values (-2, '2025-09-11 06:30:00+00', '<EMAIL>', '2025-09-11 06:30:00+00', null, '2025-09-11 06:30:00+00', '<EMAIL>', -2, 0);
insert into csms_user_csms_user_role(user_id, role) values (-6, 'Authenticated');
insert into organization_access(id, role, user_id, organization_id, version) values (-6, 'Admin', -6, -1, 0);

insert into csms_user(id, user_type, user_name, password, tenant_id, selected_organization_id, version) values (-7, 'CSMS', '<EMAIL>', null, -1, -1, 0);
insert into charging_access_token(id, country_code, party_id, token, token_type, valid_from, valid_to, last_updated, token_owner_id, version) values (-3, 'HU', 'SMART_CHARGE', 'a087229d-c5a4-490b-9526-7b3dd461fc47', 'APP_USER', '2025-09-21 07:30:00+00', null, '2025-09-21 07:30:00+00', -7, 0);
insert into charging_access_token_history(id, history_ts, change_by_user_name, valid_from, valid_to, last_updated, token_owner_user_name, charging_access_token_id, version) values (-3, '2025-09-21 07:30:00+00', '<EMAIL>', '2025-09-21 07:30:00+00', null, '2025-09-21 07:30:00+00', '<EMAIL>', -3, 0);
insert into csms_user_csms_user_role(user_id, role) values (-7, 'Authenticated');
insert into organization_access(id, role, user_id, organization_id, version) values (-7, 'User', -7, -1, 0);
insert into charging_station_access(id, role, inherited, user_id, charging_station_id, version) values (-9, 'Admin', true, -7, -1, 0);


insert into csms_user(id, user_type, user_name, password, tenant_id, selected_organization_id, version) values (-8, 'CSMS', '<EMAIL>', null, -1, -1, 0);
insert into charging_access_token(id, country_code, party_id, token, token_type, valid_from, valid_to, last_updated, token_owner_id, version) values (-4, 'HU', 'SMART_CHARGE', '98e5713d-d80d-4691-b92d-0fafa9b7d161', 'APP_USER', '2025-10-01 08:30:00+00', null, '2025-10-01 08:30:00+00', -8, 0);
insert into charging_access_token_history(id, history_ts, change_by_user_name, valid_from, valid_to, last_updated, token_owner_user_name, charging_access_token_id, version) values (-4, '2025-10-01 08:30:00+00', '<EMAIL>', '2025-10-01 08:30:00+00', null, '2025-10-01 08:30:00+00', '<EMAIL>', -4, 0);
insert into csms_user_csms_user_role(user_id, role) values (-8, 'Authenticated');
insert into organization_access(id, role, user_id, organization_id, version) values (-8, 'User', -8, -1, 0);

insert into csms_user(id, user_type, user_name, password, tenant_id, selected_organization_id, version) values (-9, 'CSMS', '<EMAIL>', null, -1, -1, 0);
insert into charging_access_token(id, country_code, party_id, token, token_type, valid_from, valid_to, last_updated, token_owner_id, version) values (-5, 'HU', 'SMART_CHARGE', 'd22ed3e9-cd39-49e4-acab-c8f859570015', 'APP_USER', '2025-10-03 09:30:00+00', null, '2025-10-03 09:30:00+00', -9, 0);
insert into charging_access_token_history(id, history_ts, change_by_user_name, valid_from, valid_to, last_updated, token_owner_user_name, charging_access_token_id, version) values (-5, '2025-10-03 09:30:00+00', '<EMAIL>', '2025-10-03 09:30:00+00', null, '2025-10-03 09:30:00+00', '<EMAIL>', -5, 0);
insert into charging_access_token(id, country_code, party_id, token, token_type, valid_from, valid_to, last_updated, token_owner_id, organization_id, version) values (-6, 'HU', 'SMART_CHARGE', 'E0040108106A24C2', 'RFID', '2025-10-03 09:30:00+00', null, '2025-10-03 09:30:00+00', -9, -1, 0);
insert into charging_access_token_history(id, history_ts, change_by_user_name, valid_from, valid_to, last_updated, token_owner_user_name, charging_access_token_id, version) values (-6, '2025-10-03 09:30:00+00', '<EMAIL>', '2025-10-03 09:30:00+00', null, '2025-10-03 09:30:00+00', '<EMAIL>', -6, 0);
insert into csms_user_csms_user_role(user_id, role) values (-9, 'Authenticated');
insert into csms_user_csms_user_role(user_id, role) values (-9, 'Maintainer');
insert into organization_access(id, role, user_id, organization_id, version) values (-9, 'Admin', -9, -1, 0);
insert into location_access(id, role, inherited, user_id, location_id, version) values (-4, 'Admin', true, -9, -1, 0);
insert into location_access(id, role, inherited, user_id, location_id, version) values (-5, 'Admin', true, -9, -2, 0);
insert into location_access(id, role, inherited, user_id, location_id, version) values (-6, 'Admin', true, -9, -3, 0);
insert into power_grid_access(id, role, inherited, user_id, power_grid_id, version) values (-8, 'Admin', true, -9, -1, 0);
insert into power_grid_access(id, role, inherited, user_id, power_grid_id, version) values (-9, 'Admin', true, -9, -2, 0);
insert into power_grid_access(id, role, inherited, user_id, power_grid_id, version) values (-10, 'Admin', true, -9, -3, 0);
insert into power_grid_access(id, role, inherited, user_id, power_grid_id, version) values (-11, 'Admin', true, -9, -4, 0);
insert into power_grid_access(id, role, inherited, user_id, power_grid_id, version) values (-12, 'Admin', true, -9, -5, 0);
insert into power_grid_access(id, role, inherited, user_id, power_grid_id, version) values (-13, 'Admin', true, -9, -6, 0);
insert into power_grid_access(id, role, inherited, user_id, power_grid_id, version) values (-14, 'Admin', true, -9, -7, 0);
insert into charging_station_access(id, role, inherited, user_id, charging_station_id, version) values (-10, 'Admin', true, -9, -1, 0);
insert into charging_station_access(id, role, inherited, user_id, charging_station_id, version) values (-11, 'Admin', true, -9, -2, 0);
insert into charging_station_access(id, role, inherited, user_id, charging_station_id, version) values (-12, 'Admin', true, -9, -3, 0);
insert into charging_station_access(id, role, inherited, user_id, charging_station_id, version) values (-13, 'Admin', true, -9, -4, 0);
insert into csms_user_tokens(id, user_name, access_token, access_token_expires_at, refresh_token, refresh_token_expires_at, auth_server_access_token, auth_server_access_token_expires_at, auth_server_refresh_token, auth_server_refresh_token_time_skew, auth_server_id_token, user_id, version)
values(-1, '<EMAIL>', 'QUJtCCcPnO', NOW() + INTERVAL '10 Minutes', 'QUJtCCcPnO', NOW() + INTERVAL '2 hour', 'dummy auth server access token string', '2025-08-01 00:00:00+00', 'dummy auth server refresh token string', null, 'dummy auth server id token string', -9, 0);

-- Insert Access group data
insert into access_group(id, name, role, organization_id, version)
values(-1, 'access group name', 'Admin', -1, 0);
insert into access_group_charging_station(access_group_id, charging_station_id)
values(-1, -1);
insert into access_group_csms_user(access_group_id, user_id)
values(-1, -7);

-- Session data
insert into session(id, session_owner_id, evse_transaction_id, country_code, party_id, create_date_time, start_date_time, end_date_time, kwh, charging_station_id, evse_id, evse_connector_id, currency, charging_periods, status, last_updated, session_token, auth_method, version) 
values (-1, -5, -1, 'hu', 'pa1', '2025-04-01 07:00:00+00', '2025-04-01 08:00:00+00', '2025-04-01 10:30:00+00', 35, -1, -1, 1, 'HUF', '[]', 'COMPLETED', '2025-04-01 10:30:00+00', '{"uid": "d6b1bfc1-22f8-4ded-9108-04c35a603d5b","type": "APP_USER","valid": true,"issuer": "SMART_CHARGE","partyId": "SMART_CHARGE", "whitelist": "NEVER", "contractId": "d6b1bfc1-22f8-4ded-9108-04c35a603d5b", "countryCode": "HU", "lastUpdated": "2025-04-01T07:00:00Z"}', 'COMMAND', 0);

insert into session(id, session_owner_id, evse_transaction_id, country_code, party_id, create_date_time, start_date_time, end_date_time, kwh, charging_station_id, evse_id, evse_connector_id, currency, charging_periods, status, last_updated, session_token, auth_method, version) 
values (-2, -5, null, 'hu', 'pa1','2025-04-01 10:00:00+00', '2025-04-01 11:00:00+00', '2025-04-01 12:45:00+00', 23, -1, -1, 2, 'HUF', '[]', 'COMPLETED', '2025-04-01 12:45:00+00', '{"uid": "d6b1bfc1-22f8-4ded-9108-04c35a603d5b","type": "APP_USER","valid": true,"issuer": "SMART_CHARGE","partyId": "SMART_CHARGE", "whitelist": "NEVER", "contractId": "d6b1bfc1-22f8-4ded-9108-04c35a603d5b", "countryCode": "HU", "lastUpdated": "2025-04-01T07:00:00Z"}', 'COMMAND', 0);

insert into session(id, session_owner_id, evse_transaction_id, country_code, party_id, create_date_time, start_date_time, end_date_time, kwh, charging_station_id, evse_id, evse_connector_id, currency, charging_periods, status, last_updated, session_token, auth_method, version)
values (-3, -5, null, 'hu', 'pa1','2025-04-02 08:15:00+00', '2025-04-02 09:15:00+00', '2025-04-02 11:20:00+00', 102, -1, -1, 1, 'HUF', '[]', 'COMPLETED', '2025-04-02 11:20:00+00', '{"uid": "d6b1bfc1-22f8-4ded-9108-04c35a603d5b","type": "APP_USER","valid": true,"issuer": "SMART_CHARGE","partyId": "SMART_CHARGE", "whitelist": "NEVER", "contractId": "d6b1bfc1-22f8-4ded-9108-04c35a603d5b", "countryCode": "HU", "lastUpdated": "2025-04-01T07:00:00Z"}', 'COMMAND', 0);

-- Meter value data for testing purposes
-- Energy_Active_Import_Register values (kWh) for charging station -1, evse 0
insert into meter_value (id, timestamp, value, context, measurand, phase, location, signed_meter_value, unit_of_measure, charging_station_id, charging_station_evse_id, version) values
(-101, NOW() - INTERVAL '7 days', 0.50, 'Sample_Clock', 'Energy_Active_Import_Register', NULL, 'Outlet', NULL, '{"unit": "Wh"}', -1, 0, 0),
(-102, NOW() - INTERVAL '6 days', 1.75, 'Sample_Clock', 'Energy_Active_Import_Register', NULL, 'Outlet', NULL, '{"unit": "Wh"}', -1, 0, 0),
(-103, NOW() - INTERVAL '5 days', 3.25, 'Sample_Clock', 'Energy_Active_Import_Register', NULL, 'Outlet', NULL, '{"unit": "Wh"}', -1, 0, 0),
(-104, NOW() - INTERVAL '4 days', 5.00, 'Sample_Clock', 'Energy_Active_Import_Register', NULL, 'Outlet', NULL, '{"unit": "Wh"}', -1, 0, 0),
(-105, NOW() - INTERVAL '3 days', 7.25, 'Sample_Clock', 'Energy_Active_Import_Register', NULL, 'Outlet', NULL, '{"unit": "Wh"}', -1, 0, 0),
(-106, NOW() - INTERVAL '2 days', 9.75, 'Sample_Clock', 'Energy_Active_Import_Register', NULL, 'Outlet', NULL, '{"unit": "Wh"}', -1, 0, 0),
(-107, NOW() - INTERVAL '1 day', 12.50, 'Sample_Clock', 'Energy_Active_Import_Register', NULL, 'Outlet', NULL, '{"unit": "Wh"}', -1, 0, 0),
(-108, NOW(), 15.00, 'Sample_Clock', 'Energy_Active_Import_Register', NULL, 'Outlet', NULL, '{"unit": "Wh"}', -1, 0, 0);

-- Power values (W) for charging station -1, evse 0
insert into meter_value (id, timestamp, value, context, measurand, phase, location, signed_meter_value, unit_of_measure, charging_station_id, charging_station_evse_id, version) values
(-109, NOW() - INTERVAL '7 days', 3500.00, 'Sample_Clock', 'Power_Active_Import', NULL, 'Outlet', NULL, '{"unit": "W"}', -1, 0, 0),
(-110, NOW() - INTERVAL '6 days', 3750.00, 'Sample_Clock', 'Power_Active_Import', NULL, 'Outlet', NULL, '{"unit": "W"}', -1, 0, 0),
(-111, NOW() - INTERVAL '5 days', 3600.00, 'Sample_Clock', 'Power_Active_Import', NULL, 'Outlet', NULL, '{"unit": "W"}', -1, 0, 0),
(-112, NOW() - INTERVAL '4 days', 3800.00, 'Sample_Clock', 'Power_Active_Import', NULL, 'Outlet', NULL, '{"unit": "W"}', -1, 0, 0),
(-113, NOW() - INTERVAL '3 days', 3650.00, 'Sample_Clock', 'Power_Active_Import', NULL, 'Outlet', NULL, '{"unit": "W"}', -1, 0, 0),
(-114, NOW() - INTERVAL '2 days', 3700.00, 'Sample_Clock', 'Power_Active_Import', NULL, 'Outlet', NULL, '{"unit": "W"}', -1, 0, 0),
(-115, NOW() - INTERVAL '1 day', 3550.00, 'Sample_Clock', 'Power_Active_Import', NULL, 'Outlet', NULL, '{"unit": "W"}', -1, 0, 0),
(-116, NOW(), 3600.00, 'Sample_Clock', 'Power_Active_Import', NULL, 'Outlet', NULL, '{"unit": "W"}', -1, 0, 0);

-- Current values (A) for charging station -1, evse 0
insert into meter_value (id, timestamp, value, context, measurand, phase, location, signed_meter_value, unit_of_measure, charging_station_id, charging_station_evse_id, version) values
(-117, NOW() - INTERVAL '7 days', 15.91, 'Sample_Clock', 'Current_Import', 'L1', 'Outlet', NULL, '{"unit": "A"}', -1, 0, 0),
(-118, NOW() - INTERVAL '6 days', 17.05, 'Sample_Clock', 'Current_Import', 'L1', 'Outlet', NULL, '{"unit": "A"}', -1, 0, 0),
(-119, NOW() - INTERVAL '5 days', 16.36, 'Sample_Clock', 'Current_Import', 'L1', 'Outlet', NULL, '{"unit": "A"}', -1, 0, 0),
(-120, NOW() - INTERVAL '4 days', 17.27, 'Sample_Clock', 'Current_Import', 'L1', 'Outlet', NULL, '{"unit": "A"}', -1, 0, 0),
(-121, NOW() - INTERVAL '3 days', 16.59, 'Sample_Clock', 'Current_Import', 'L1', 'Outlet', NULL, '{"unit": "A"}', -1, 0, 0),
(-122, NOW() - INTERVAL '2 days', 16.82, 'Sample_Clock', 'Current_Import', 'L1', 'Outlet', NULL, '{"unit": "A"}', -1, 0, 0),
(-123, NOW() - INTERVAL '1 day', 16.14, 'Sample_Clock', 'Current_Import', 'L1', 'Outlet', NULL, '{"unit": "A"}', -1, 0, 0),
(-124, NOW(), 16.36, 'Sample_Clock', 'Current_Import', 'L1', 'Outlet', NULL, '{"unit": "A"}', -1, 0, 0);

-- Voltage values (V) for charging station -1, evse 0
insert into meter_value (id, timestamp, value, context, measurand, phase, location, signed_meter_value, unit_of_measure, charging_station_id, charging_station_evse_id, version) values
(-125, NOW() - INTERVAL '7 days', 220.00, 'Sample_Clock', 'Voltage', 'L1', 'Outlet', NULL, '{"unit": "V"}', -1, 0, 0),
(-126, NOW() - INTERVAL '6 days', 221.50, 'Sample_Clock', 'Voltage', 'L1', 'Outlet', NULL, '{"unit": "V"}', -1, 0, 0),
(-127, NOW() - INTERVAL '5 days', 219.75, 'Sample_Clock', 'Voltage', 'L1', 'Outlet', NULL, '{"unit": "V"}', -1, 0, 0),
(-128, NOW() - INTERVAL '4 days', 222.25, 'Sample_Clock', 'Voltage', 'L1', 'Outlet', NULL, '{"unit": "V"}', -1, 0, 0),
(-129, NOW() - INTERVAL '3 days', 220.50, 'Sample_Clock', 'Voltage', 'L1', 'Outlet', NULL, '{"unit": "V"}', -1, 0, 0),
(-130, NOW() - INTERVAL '2 days', 221.00, 'Sample_Clock', 'Voltage', 'L1', 'Outlet', NULL, '{"unit": "V"}', -1, 0, 0),
(-131, NOW() - INTERVAL '1 day', 219.25, 'Sample_Clock', 'Voltage', 'L1', 'Outlet', NULL, '{"unit": "V"}', -1, 0, 0),
(-132, NOW(), 220.75, 'Sample_Clock', 'Voltage', 'L1', 'Outlet', NULL, '{"unit": "V"}', -1, 0, 0);


-- Insert floor data
insert into floor(id, location_id, level, name, layout, row_count, column_count, version)
values (-1, -1, 0, 'DefaultFloor', 'Rectangle', 10, 10, 0);