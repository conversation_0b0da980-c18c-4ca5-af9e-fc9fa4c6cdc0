-- Add foreign key constraints

-- Location foreign keys
ALTER TABLE organization 
ADD CONSTRAINT fk_organization_tenant 
FOREIGN KEY (tenant_id) REFERENCES tenant(id);

-- Location foreign keys
ALTER TABLE location 
ADD CONSTRAINT fk_location_organization 
FOREIGN KEY (organization_id) REFERENCES organization(id);

-- Floor foreign keys
ALTER TABLE floor 
ADD CONSTRAINT fk_floor_location 
FOREIGN KEY (location_id) REFERENCES location(id);

-- Power grid foreign keys
ALTER TABLE power_grid 
ADD CONSTRAINT fk_power_grid_location 
FOREIGN KEY (location_id) REFERENCES location(id);

-- Charging station foreign keys
ALTER TABLE charging_station 
ADD CONSTRAINT fk_charging_station_csms_user 
FOREIGN KEY (user_id) REFERENCES csms_user(id);

ALTER TABLE charging_station 
ADD CONSTRAINT fk_charging_station_power_grid 
FOREIGN KEY (power_grid_id) REFERENCES power_grid(id);

-- EVSE foreign keys
ALTER TABLE evse 
ADD CONSTRAINT fk_evse_charging_station 
FOREIGN KEY (charging_station_id) REFERENCES charging_station(id);

-- Connector foreign keys
ALTER TABLE connector 
ADD CONSTRAINT fk_connector_evse 
FOREIGN KEY (evse_id) REFERENCES evse(id);

-- EVSE transaction foreign keys
ALTER TABLE evse_transaction 
ADD CONSTRAINT fk_evse_transaction_evse 
FOREIGN KEY (evse_id) REFERENCES evse(id);

ALTER TABLE evse_transaction 
ADD CONSTRAINT fk_evse_transaction_connector 
FOREIGN KEY (connector_id) REFERENCES connector(id);

-- EVSE transaction event foreign keys
ALTER TABLE evse_transaction_event 
ADD CONSTRAINT fk_evse_transaction_event_transaction 
FOREIGN KEY (transaction_id) REFERENCES evse_transaction(id);

-- Charging station variable foreign keys
ALTER TABLE charging_station_variable 
ADD CONSTRAINT fk_charging_station_variable_charging_station 
FOREIGN KEY (charging_station_id) REFERENCES charging_station(id);

-- Charging station variable value foreign keys
ALTER TABLE charging_station_variable_value 
ADD CONSTRAINT fk_charging_station_variable_value_variable 
FOREIGN KEY (charging_station_variable_id) REFERENCES charging_station_variable(id);

-- Report foreign keys
ALTER TABLE report 
ADD CONSTRAINT fk_report_charging_station 
FOREIGN KEY (charging_station_id) REFERENCES charging_station(id);

-- Meter value foreign keys
ALTER TABLE meter_value 
ADD CONSTRAINT fk_meter_value_charging_station 
FOREIGN KEY (charging_station_id) REFERENCES charging_station(id);

-- CSMS user foreign keys
ALTER TABLE csms_user 
ADD CONSTRAINT fk_csms_user_tenant 
FOREIGN KEY (tenant_id) REFERENCES tenant(id);

ALTER TABLE csms_user 
ADD CONSTRAINT fk_csms_user_selected_organization 
FOREIGN KEY (selected_organization_id) REFERENCES organization(id);

-- CSMS csms_user_csms_user_role foreign keys
ALTER TABLE csms_user_csms_user_role 
ADD CONSTRAINT fk_csms_user_csms_user_role_csms_user
FOREIGN KEY (user_id) REFERENCES csms_user(id);

ALTER TABLE csms_user_csms_user_role 
ADD CONSTRAINT fk_csms_user_csms_user_role_csms_user_role
FOREIGN KEY (role) REFERENCES csms_user_role(role);

-- CSMS user tokens foreign keys
ALTER TABLE csms_user_tokens
ADD CONSTRAINT fk_csms_user_tokens_csms_user 
FOREIGN KEY (user_id) REFERENCES csms_user(id);

-- Session foreign keys
ALTER TABLE session 
ADD CONSTRAINT fk_session_session_owner 
FOREIGN KEY (session_owner_id) REFERENCES csms_user(id);

ALTER TABLE session 
ADD CONSTRAINT fk_session_evse_transaction 
FOREIGN KEY (evse_transaction_id) REFERENCES evse_transaction(id);

-- Access group foreign keys
ALTER TABLE access_group 
ADD CONSTRAINT fk_access_group_organization 
FOREIGN KEY (organization_id) REFERENCES organization(id);

-- Access group location foreign keys
ALTER TABLE access_group_location 
ADD CONSTRAINT fk_access_group_location_access_group
FOREIGN KEY (access_group_id) REFERENCES access_group(id);

ALTER TABLE access_group_location 
ADD CONSTRAINT fk_access_group_location_location
FOREIGN KEY (location_id) REFERENCES location(id);

-- Access group power foreign keys
ALTER TABLE access_group_power_grid 
ADD CONSTRAINT fk_access_group_power_grid_access_group
FOREIGN KEY (access_group_id) REFERENCES access_group(id);

ALTER TABLE access_group_power_grid 
ADD CONSTRAINT fk_access_group_power_grid_power_grid
FOREIGN KEY (power_grid_id) REFERENCES power_grid(id);

-- Access group charging station foreign keys
ALTER TABLE access_group_charging_station 
ADD CONSTRAINT fk_access_group_charging_station_access_group
FOREIGN KEY (access_group_id) REFERENCES access_group(id);

ALTER TABLE access_group_charging_station 
ADD CONSTRAINT fk_access_group_charging_station_charging_station
FOREIGN KEY (charging_station_id) REFERENCES charging_station(id);

-- Access group csms user foreign keys
ALTER TABLE access_group_csms_user 
ADD CONSTRAINT fk_access_group_csms_user_access_group
FOREIGN KEY (access_group_id) REFERENCES access_group(id);

ALTER TABLE access_group_csms_user 
ADD CONSTRAINT fk_access_group_csms_user_csms_user
FOREIGN KEY (user_id) REFERENCES csms_user(id);

-- Organization access foreign keys
ALTER TABLE organization_access 
ADD CONSTRAINT fk_organization_access_csms_user
FOREIGN KEY (user_id) REFERENCES csms_user(id);

ALTER TABLE organization_access 
ADD CONSTRAINT fk_organization_access_organization
FOREIGN KEY (organization_id) REFERENCES organization(id);

-- Location access foreign keys
ALTER TABLE location_access 
ADD CONSTRAINT fk_location_access_csms_user
FOREIGN KEY (user_id) REFERENCES csms_user(id);

ALTER TABLE location_access 
ADD CONSTRAINT fk_location_access_location
FOREIGN KEY (location_id) REFERENCES location(id);

-- Power grid access foreign keys
ALTER TABLE power_grid_access 
ADD CONSTRAINT fk_power_grid_access_csms_user
FOREIGN KEY (user_id) REFERENCES csms_user(id);

ALTER TABLE power_grid_access 
ADD CONSTRAINT fk_power_grid_access_power_grid
FOREIGN KEY (power_grid_id) REFERENCES power_grid(id);

-- Charging station access foreign keys
ALTER TABLE charging_station_access 
ADD CONSTRAINT fk_charging_station_access_csms_user
FOREIGN KEY (user_id) REFERENCES csms_user(id);

ALTER TABLE charging_station_access 
ADD CONSTRAINT fk_charging_station_access_charging_station
FOREIGN KEY (charging_station_id) REFERENCES charging_station(id);

-- Charging access token foreign keys
ALTER TABLE charging_access_token 
ADD CONSTRAINT fk_charging_access_token_csms_user
FOREIGN KEY (token_owner_id) REFERENCES csms_user(id);

ALTER TABLE charging_access_token 
ADD CONSTRAINT fk_charging_access_token_organization
FOREIGN KEY (organization_id) REFERENCES organization(id);

