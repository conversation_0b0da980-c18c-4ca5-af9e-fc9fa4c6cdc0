#!/bin/bash

# Exit immediately if a command exits with a non-zero status.
set -e

# Function to handle errors
error_exit() {
  echo "ERROR: The script failed at step: $1"
  exit 1
}

# The original Windows project directory, for copying files back.
ORIGINAL_PROJECT_DIR="/mnt/d/devtools/project/charger/smart-charge"

if [ "$1" == "wsl" ]; then
  echo "Running in WSL environment"
  echo "Deleting old project directory..."
  rm -rf ~/dev/smart-charge || error_exit "Deleting old project directory"
  echo "Copying project directory..."
  mkdir -p ~/dev || error_exit "Creating dev directory"
  cp -r "$ORIGINAL_PROJECT_DIR" ~/dev || error_exit "Copying project directory"
  echo "Changing to project directory..."
  cd ~/dev/smart-charge || error_exit "Changing to project directory"
  echo "Fixing gradlew line endings and permissions..."
  sed -i 's/\r$//' gradlew || error_exit "Fixing gradlew line endings"
  chmod +x gradlew || error_exit "Setting gradlew permissions"
fi

PROPERTIES_FILE="src/main/resources/application.properties"
GRADLE_FILE="build.gradle"

# Function to revert all changes on error
cleanup() {
  echo "An error occurred. Reverting changes..."
  # Revert build.gradle if a backup exists
  if [ -f "$GRADLE_FILE.original" ]; then
    mv "$GRADLE_FILE.original" "$GRADLE_FILE"
    echo "Restored $GRADLE_FILE from backup."
  fi
  # Revert application.properties
  sed -i 's/^quarkus.native.enabled=true/#quarkus.native.enabled=true/' "$PROPERTIES_FILE"
  sed -i 's/^quarkus.package.jar.enabled=false/#quarkus.package.jar.enabled=false/' "$PROPERTIES_FILE"
}

# Trap any script exit to run the cleanup function
trap cleanup EXIT

echo "STEP 1: Incrementing patch version in $GRADLE_FILE..."

# Backup the original file
cp "$GRADLE_FILE" "$GRADLE_FILE.original"

# Extract current version, increment patch number, and replace in file
# Matches: version = "X.Y.Z" or version = "X.Y.Z-SNAPSHOT" (with optional suffix)
CURRENT_VERSION=$(grep -oP '^\s*version\s*=\s*"\K[^"]+' "$GRADLE_FILE")
if [ -z "$CURRENT_VERSION" ]; then
  error_exit "Could not find version string in $GRADLE_FILE"
fi

# Parse version components (handles versions like 1.0.4 or 1.0.4-SNAPSHOT)
MAJOR=$(echo "$CURRENT_VERSION" | cut -d. -f1)
MINOR=$(echo "$CURRENT_VERSION" | cut -d. -f2)
PATCH_WITH_SUFFIX=$(echo "$CURRENT_VERSION" | cut -d. -f3)
PATCH=$(echo "$PATCH_WITH_SUFFIX" | grep -oP '^\d+')
SUFFIX=$(echo "$PATCH_WITH_SUFFIX" | grep -oP '(?<=^\d+).*' || echo "")

# Increment patch version
NEW_PATCH=$((PATCH + 1))
NEW_VERSION="${MAJOR}.${MINOR}.${NEW_PATCH}${SUFFIX}"

echo "Incrementing version from $CURRENT_VERSION to $NEW_VERSION"

# Replace the version in the file
sed -i "s/^\(\s*version\s*=\s*\"\)${CURRENT_VERSION}\"/\1${NEW_VERSION}\"/" "$GRADLE_FILE"

# Verify that the version was actually incremented
if cmp -s "$GRADLE_FILE" "$GRADLE_FILE.original"; then
  error_exit "Incrementing version in '$GRADLE_FILE' failed. The file was not modified."
fi
echo "Version incremented successfully."



echo "STEP 2: Uncommenting native build properties..."
sed -i 's/^#quarkus.native.enabled=true/quarkus.native.enabled=true/' "$PROPERTIES_FILE" || error_exit "Uncommenting quarkus.native.enabled"
sed -i 's/^#quarkus.package.jar.enabled=false/quarkus.package.jar.enabled=false/' "$PROPERTIES_FILE" || error_exit "Uncommenting quarkus.package.jar.enabled"

echo "STEP 3: Building native runnable..."
./gradlew build -x test -Dquarkus.package.type=native -Dquarkus.native.container-build=true -Dquarkus.native.container-runtime=docker || error_exit "Building native runnable with Gradle"

echo "STEP 4: Building the new docker image..."
docker build -f src/main/docker/Dockerfile.native -t smart-charge-backend:latest . || error_exit "Building Docker image"

echo "STEP 5: Re-commenting native build properties..."
sed -i 's/^quarkus.native.enabled=true/#quarkus.native.enabled=true/' "$PROPERTIES_FILE" || error_exit "Re-commenting quarkus.native.enabled"
sed -i 's/^quarkus.package.jar.enabled=false/#quarkus.package.jar.enabled=false/' "$PROPERTIES_FILE" || error_exit "Re-commenting quarkus.package.jar.enabled"

echo "STEP 6: Saving the new image to file..."
docker save -o backend.tar smart-charge-backend:latest || error_exit "Saving Docker image"

if [ "$1" == "wsl" ]; then
  echo "STEP 7: Copying artifacts back to original Windows directory..."
  cp backend.tar "$ORIGINAL_PROJECT_DIR/" || error_exit "Copying backend.tar"
  cp "$GRADLE_FILE" "$ORIGINAL_PROJECT_DIR/" || error_exit "Copying build.gradle"
  echo "Copied backend.tar and build.gradle to $ORIGINAL_PROJECT_DIR"
fi

echo "Native image build script finished successfully."

# Disable the trap and remove the backup file on successful execution
trap - EXIT
rm -f "$GRADLE_FILE.original"
exit 0