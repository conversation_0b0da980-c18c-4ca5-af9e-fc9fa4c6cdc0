# pack-refs with: peeled fully-peeled sorted 
ff0b2e81ad94b06991318a0713eee09a24831616 refs/remotes/origin/feat/access-entities
7b56c3e001a47a1fd108677a5f5ceeefd8a76c31 refs/remotes/origin/feat/add-Multitenancy-support
92267f248f3b5fcb37b6e45ce4ee906bf9fcad28 refs/remotes/origin/feat/add-selected-organization-role-of-the-users
778dea4a97252d713def7553deefa299b05310d2 refs/remotes/origin/feat/usage-of-optional-part6
92fd5f6275ba4a5899d30bcd77ec2496c023a445 refs/remotes/origin/feature/charging_session
10a84021d695ccfd2c8ca53b5ea2c81dc3ba3b99 refs/remotes/origin/feature/improvement
82cb62052dc5b1f3270911954e7dedc2ccf04a8f refs/remotes/origin/feature/quarkusDev_with_permanent_database
7e7bf1188834dae6578e61e5184bf66af41fc848 refs/remotes/origin/feature/status_update_gql_subscription
f1016dbccd76e1d42a17b2ad29ab7c928dab6a85 refs/remotes/origin/feature/transaction
80f6e360af58695ed70f32945f70ff89bce99af0 refs/remotes/origin/fix/CriteriaProviderFactories-static-problem
02aa5802df958cb24b1f5e6726c69a4b8de40652 refs/remotes/origin/fix/null_pointer_fix
0b0233939094d8ce4399226980b2fc3509f48f81 refs/remotes/origin/main
dfc45679bb75b498e9e136a9d42c7bd73017aa01 refs/remotes/origin/set-sast-config-1
