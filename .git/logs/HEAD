0000000000000000000000000000000000000000 0b0233939094d8ce4399226980b2fc3509f48f81 malloc <a@a.a> 1764778420 +0100	clone: from gitlab.com:galiasystems/smart-charge.git
0b0233939094d8ce4399226980b2fc3509f48f81 0b0233939094d8ce4399226980b2fc3509f48f81 malloc <a@a.a> 1764779089 +0100	checkout: moving from main to feature/metrics
0b0233939094d8ce4399226980b2fc3509f48f81 0b0233939094d8ce4399226980b2fc3509f48f81 malloc <a@a.a> 1764779141 +0100	checkout: moving from feature/metrics to main
0b0233939094d8ce4399226980b2fc3509f48f81 8eda302c613cf841daa368e839cb1463dfec3bcb malloc <a@a.a> 1764779210 +0100	checkout: moving from main to feature/metrics
8eda302c613cf841daa368e839cb1463dfec3bcb bdca024cfa06a5bd70d687216365b9490f50d067 malloc <a@a.a> 1764866663 +0100	commit: feat: otel settings
bdca024cfa06a5bd70d687216365b9490f50d067 db6f2108e47346c4383bd96b1876c302affe3ed0 malloc <a@a.a> 1764867484 +0100	commit: feat: add prometheus
db6f2108e47346c4383bd96b1876c302affe3ed0 4e39059f1de3b8765a551bf857cf68bb36958bf7 malloc <a@a.a> 1764867647 +0100	commit: feat: add prometheus
4e39059f1de3b8765a551bf857cf68bb36958bf7 **************************************** malloc <a@a.a> 1764948183 +0100	commit: feat: add ocpp metrics
**************************************** a1b7d9862836fbcea0503268d97710c4d7c4b167 malloc <a@a.a> 1764948597 +0100	commit (merge): Merge remote-tracking branch 'origin/main' into feature/metrics
a1b7d9862836fbcea0503268d97710c4d7c4b167 0b50fe70b0675b0801fc0b3eb752f1acf2c78b9d malloc <a@a.a> 1764955217 +0100	commit: fix: clean up
0b50fe70b0675b0801fc0b3eb752f1acf2c78b9d 0b0233939094d8ce4399226980b2fc3509f48f81 malloc <a@a.a> 1765180764 +0100	checkout: moving from feature/metrics to main
0b0233939094d8ce4399226980b2fc3509f48f81 63e51092d6ee01db6999258e2f9ce9b21c22b04a malloc <a@a.a> 1765181006 +0100	merge origin/main: Fast-forward
63e51092d6ee01db6999258e2f9ce9b21c22b04a 63e51092d6ee01db6999258e2f9ce9b21c22b04a malloc <a@a.a> 1765228668 +0100	checkout: moving from main to feature/metrics_2
63e51092d6ee01db6999258e2f9ce9b21c22b04a 44cd72d7f1b1ecb497a348921e7034a8e78c158a malloc <a@a.a> 1765228816 +0100	commit: fix: metrics on demo env
44cd72d7f1b1ecb497a348921e7034a8e78c158a 63e51092d6ee01db6999258e2f9ce9b21c22b04a malloc <a@a.a> 1765276665 +0100	checkout: moving from feature/metrics_2 to main
63e51092d6ee01db6999258e2f9ce9b21c22b04a fa564f7b237d09d5b188ccf00dae2757ad3286c1 malloc <a@a.a> 1765276677 +0100	merge origin/main: Fast-forward
fa564f7b237d09d5b188ccf00dae2757ad3286c1 fa564f7b237d09d5b188ccf00dae2757ad3286c1 malloc <a@a.a> 1765280447 +0100	checkout: moving from main to fix/runtime_init
fa564f7b237d09d5b188ccf00dae2757ad3286c1 8a1b647a2697afb3aea9d042debaf443dd95ec7f malloc <a@a.a> 1765280488 +0100	commit: fix: missing model from runtime init
8a1b647a2697afb3aea9d042debaf443dd95ec7f fa564f7b237d09d5b188ccf00dae2757ad3286c1 malloc <a@a.a> 1765294211 +0100	checkout: moving from fix/runtime_init to main
fa564f7b237d09d5b188ccf00dae2757ad3286c1 338a008059ff6cc799592ed6a562a1809f121831 malloc <a@a.a> 1765294223 +0100	merge origin/main: Fast-forward
338a008059ff6cc799592ed6a562a1809f121831 338a008059ff6cc799592ed6a562a1809f121831 malloc <a@a.a> 1765294237 +0100	checkout: moving from main to feature/metrics_improvement
338a008059ff6cc799592ed6a562a1809f121831 a1cf07028b4cea02af6121a93900ec2b1bbebdde malloc <a@a.a> 1765303202 +0100	commit: feat: time-based metrics
a1cf07028b4cea02af6121a93900ec2b1bbebdde 338a008059ff6cc799592ed6a562a1809f121831 malloc <a@a.a> 1765348291 +0100	checkout: moving from feature/metrics_improvement to main
338a008059ff6cc799592ed6a562a1809f121831 47a9f4fc02cc8f4d6b9cbf1209d1082d4dc8622a malloc <a@a.a> 1765348663 +0100	merge origin/main: Fast-forward
47a9f4fc02cc8f4d6b9cbf1209d1082d4dc8622a 47a9f4fc02cc8f4d6b9cbf1209d1082d4dc8622a malloc <a@a.a> 1765348811 +0100	checkout: moving from main to feature/native_image_build
47a9f4fc02cc8f4d6b9cbf1209d1082d4dc8622a 26b35d4ad37fd9d8bc634532cbe9fd98b8a69df1 malloc <a@a.a> 1765371686 +0100	merge origin/main: Fast-forward
26b35d4ad37fd9d8bc634532cbe9fd98b8a69df1 41d14071b836f3ef207a81f2a4e66b8d752cdcf6 malloc <a@a.a> 1765387969 +0100	commit: feat: native image build script + performance test enhancement
41d14071b836f3ef207a81f2a4e66b8d752cdcf6 47a9f4fc02cc8f4d6b9cbf1209d1082d4dc8622a malloc <a@a.a> 1765448817 +0100	checkout: moving from feature/native_image_build to main
47a9f4fc02cc8f4d6b9cbf1209d1082d4dc8622a 7fc2da0ef42a5ea7a2de2c9a0c84bc59d792715a malloc <a@a.a> 1765448846 +0100	merge origin/main: Fast-forward
7fc2da0ef42a5ea7a2de2c9a0c84bc59d792715a 7fc2da0ef42a5ea7a2de2c9a0c84bc59d792715a malloc <a@a.a> 1765532991 +0100	checkout: moving from main to feature/sessions_enhancement
7fc2da0ef42a5ea7a2de2c9a0c84bc59d792715a 133fb80dab06557bb301a541a97e12996d2eee4f malloc <a@a.a> 1765535950 +0100	commit: feat: add session owner field resolver
133fb80dab06557bb301a541a97e12996d2eee4f 7fc2da0ef42a5ea7a2de2c9a0c84bc59d792715a malloc <a@a.a> 1765555630 +0100	checkout: moving from feature/sessions_enhancement to main
7fc2da0ef42a5ea7a2de2c9a0c84bc59d792715a 8c77e9b65eae7cff0da2fb89db5dcc231eb1bcb8 malloc <a@a.a> 1765555655 +0100	merge origin/main: Fast-forward
8c77e9b65eae7cff0da2fb89db5dcc231eb1bcb8 8c77e9b65eae7cff0da2fb89db5dcc231eb1bcb8 malloc <a@a.a> 1765733189 +0100	checkout: moving from main to fix/docker_compose_demo_env
8c77e9b65eae7cff0da2fb89db5dcc231eb1bcb8 a8d02712f8600537e8c0958c2b7944f5d41e9f61 malloc <a@a.a> 1765733212 +0100	commit: fix: demo env compose file
a8d02712f8600537e8c0958c2b7944f5d41e9f61 8c77e9b65eae7cff0da2fb89db5dcc231eb1bcb8 malloc <a@a.a> 1765973309 +0100	checkout: moving from fix/docker_compose_demo_env to main
8c77e9b65eae7cff0da2fb89db5dcc231eb1bcb8 fc0f25d2d16a3625aabd918a07912c07d739f3e1 malloc <a@a.a> 1765973324 +0100	merge origin/main: Fast-forward
fc0f25d2d16a3625aabd918a07912c07d739f3e1 fc0f25d2d16a3625aabd918a07912c07d739f3e1 malloc <a@a.a> 1765973362 +0100	checkout: moving from main to feature/charging_stations_stuck_in_online_state
fc0f25d2d16a3625aabd918a07912c07d739f3e1 2311b1848305e9df088b1772c83c5891df39f8a4 malloc <a@a.a> 1765984963 +0100	commit: feat: set all Charging Stations to Offline on startup
2311b1848305e9df088b1772c83c5891df39f8a4 a5582fb434444ef8b1bc251b3ce9ce2df514d2a7 malloc <a@a.a> 1765989399 +0100	commit: fix: eliminate await().indefinitly()
a5582fb434444ef8b1bc251b3ce9ce2df514d2a7 e076cb917163eb911a385fc347a7195d8405c569 malloc <a@a.a> 1765990258 +0100	commit: fix: use Criteria api
e076cb917163eb911a385fc347a7195d8405c569 c33b20a8d7db0b913b41818b194eb05bad9537bf malloc <a@a.a> 1765995060 +0100	commit: fix: update filtering and blocking update
c33b20a8d7db0b913b41818b194eb05bad9537bf fc0f25d2d16a3625aabd918a07912c07d739f3e1 malloc <a@a.a> 1766067050 +0100	checkout: moving from feature/charging_stations_stuck_in_online_state to main
fc0f25d2d16a3625aabd918a07912c07d739f3e1 419d8c52e3b2e05623ead58a2d1d3a74fbb8d70c malloc <a@a.a> 1766067068 +0100	merge origin/main: Fast-forward
419d8c52e3b2e05623ead58a2d1d3a74fbb8d70c 419d8c52e3b2e05623ead58a2d1d3a74fbb8d70c malloc <a@a.a> 1766067081 +0100	checkout: moving from main to feature/report_enhancement
419d8c52e3b2e05623ead58a2d1d3a74fbb8d70c da64aa551e031d429422dba1e20957ce75e9c9da malloc <a@a.a> 1767442511 +0100	merge main: Fast-forward
da64aa551e031d429422dba1e20957ce75e9c9da bf8013f7e6a4c097d140b881c5b763f95ac60312 malloc <a@a.a> 1767454695 +0100	commit: conductor(setup): Add conductor set up files
bf8013f7e6a4c097d140b881c5b763f95ac60312 dead665d8bcfeb86379ce05ec14ae43f696f217d malloc <a@a.a> 1767459336 +0100	commit: conductor(checkpoint): Checkpoint end of Phase 1 - Header and Metadata Implementation
dead665d8bcfeb86379ce05ec14ae43f696f217d 3122968fbaf5702df24d4f52f13b7559489476f0 malloc <a@a.a> 1767459348 +0100	commit: conductor(plan): Mark phase 'Header and Metadata Implementation' as complete
3122968fbaf5702df24d4f52f13b7559489476f0 4e401f5f1133332c3b72c7d26fae18b290cd771c malloc <a@a.a> 1767465036 +0100	commit: Enhance Excel report with professional header, session enrichment, zebra striping, and summary formulas
4e401f5f1133332c3b72c7d26fae18b290cd771c 967166671df96622819cbaf3b0a9eb966659c9a0 malloc <a@a.a> 1767466059 +0100	commit: Fix IllegalStateException (Session closed) by refactoring enrichment to be sequential
967166671df96622819cbaf3b0a9eb966659c9a0 d439cc2a70ab5b43ab13f1e26d1dceb3ec4503f5 malloc <a@a.a> 1767518708 +0100	commit: fix: filtering for organizationId
d439cc2a70ab5b43ab13f1e26d1dceb3ec4503f5 bb3a127ab858502ea01a499ca5bfdd4c56bc7285 malloc <a@a.a> 1767522176 +0100	commit: feat: enhance session report generation with organization name and metadata
bb3a127ab858502ea01a499ca5bfdd4c56bc7285 713f3c2b3d0fd5ee398b5c47b6408b9387988df3 malloc <a@a.a> 1767523355 +0100	commit: feat(reporting): Update PDF generator contract and data retrieval
713f3c2b3d0fd5ee398b5c47b6408b9387988df3 910d49a5a3e48862819818ce3d65e3828c2a986f malloc <a@a.a> 1767523389 +0100	commit: conductor(plan): Mark Phase 1 as complete
910d49a5a3e48862819818ce3d65e3828c2a986f 98a3dc0bb9f728979a63928ca777b9a4767b4410 malloc <a@a.a> 1767523703 +0100	commit: feat(reporting): Implement PDF layout with title, metadata, summary and multi-page support
98a3dc0bb9f728979a63928ca777b9a4767b4410 8370a0c2b97c6cabffc1303c6463c101c60ee21b malloc <a@a.a> 1767523724 +0100	commit: conductor(plan): Mark Phase 2 as complete
8370a0c2b97c6cabffc1303c6463c101c60ee21b 0d410d94f1efaa570a09c98d331f911cce560eef malloc <a@a.a> 1767523832 +0100	commit: test(reporting): Add multi-page test case for PDF generator
0d410d94f1efaa570a09c98d331f911cce560eef fd6cc461b5225ddd29534f328ce291d06035b697 malloc <a@a.a> 1767523849 +0100	commit: conductor(plan): Mark Phase 3 as complete
fd6cc461b5225ddd29534f328ce291d06035b697 dfceee16d59b9903f03c8612b960220d8fac0f1b malloc <a@a.a> 1767525310 +0100	commit: feat(reporting): Enhance PDF report grid with 8 columns, zebra coloring, 8pt font and dynamic row height with text wrapping
dfceee16d59b9903f03c8612b960220d8fac0f1b 51fb090dc7181696e3c70e6142cde67fdc31d0dd malloc <a@a.a> 1767528182 +0100	commit: feat(reporting): Optimize PDF session report column widths with weighted distribution
51fb090dc7181696e3c70e6142cde67fdc31d0dd 1c394d473f0d0a057a6a3210d3762b3dcd2fc75c malloc <a@a.a> 1767541734 +0100	commit: fix: column width
1c394d473f0d0a057a6a3210d3762b3dcd2fc75c 6d48bb180470aa5b163673c17f45e404a512aa28 malloc <a@a.a> 1767542724 +0100	commit: fix: tests
6d48bb180470aa5b163673c17f45e404a512aa28 caf55fe5cd905d6cf794129e57296ba5880bcd63 malloc <a@a.a> 1767547183 +0100	commit: fix: clean up
caf55fe5cd905d6cf794129e57296ba5880bcd63 8011241290a99ab0e88e861ab5f734cbc1651f86 malloc <a@a.a> 1767602781 +0100	commit: conductor(checkpoint): Checkpoint end of Phase 1 - Analysis & Baseline
8011241290a99ab0e88e861ab5f734cbc1651f86 67180f0e5f75f970816bc8cae14ea912542618fc malloc <a@a.a> 1767602800 +0100	commit: conductor(plan): Mark phase 'Analysis & Baseline' as complete
67180f0e5f75f970816bc8cae14ea912542618fc 510a1d3168b525c6ea847ea9adf4bcfd97a621fc malloc <a@a.a> 1767603313 +0100	commit: conductor(checkpoint): Checkpoint end of Phase 2 - TDD - Red Phase
510a1d3168b525c6ea847ea9adf4bcfd97a621fc 8c62e89fd5eaf13676b5f2af3290760f02d57dd8 malloc <a@a.a> 1767603328 +0100	commit: conductor(plan): Mark phase 'TDD - Red Phase' as complete
8c62e89fd5eaf13676b5f2af3290760f02d57dd8 98cdcd8ffcdbd2bf69ed10e9f7e56db988cf2b5a malloc <a@a.a> 1767604559 +0100	commit: feat(csms): Optimize entity fetching in SessionServiceImpl
98cdcd8ffcdbd2bf69ed10e9f7e56db988cf2b5a 077ed60b96d7b9c13f06fac05fbeab92470b076b malloc <a@a.a> 1767604576 +0100	commit: conductor(plan): Mark track as complete
077ed60b96d7b9c13f06fac05fbeab92470b076b 8f6f88e19088a192e9a00503430dafc2b0399cc3 malloc <a@a.a> 1767604722 +0100	commit: fix: clean up
8f6f88e19088a192e9a00503430dafc2b0399cc3 2adcc2f26073bc5f44c135e3a11558c0cfa55e48 malloc <a@a.a> 1767605809 +0100	commit: fix: get organization id
2adcc2f26073bc5f44c135e3a11558c0cfa55e48 b7087d875529afac4eaae8235936d63817ccaa6e malloc <a@a.a> 1767607877 +0100	commit: fix: test and clean up
b7087d875529afac4eaae8235936d63817ccaa6e f998c7d612d97b9006e771ac6473b2127955fcf4 malloc <a@a.a> 1767619619 +0100	commit: fix: fetch data for enriched sessions
f998c7d612d97b9006e771ac6473b2127955fcf4 da64aa551e031d429422dba1e20957ce75e9c9da malloc <a@a.a> 1767690916 +0100	checkout: moving from feature/report_enhancement to main
da64aa551e031d429422dba1e20957ce75e9c9da fdb8975f4fbb708667312c287a9b316533d48acc malloc <a@a.a> 1767691092 +0100	merge origin/main: Fast-forward
fdb8975f4fbb708667312c287a9b316533d48acc fdb8975f4fbb708667312c287a9b316533d48acc malloc <a@a.a> 1767790596 +0100	checkout: moving from main to feature/deploy_enhancement
