[core]
	repositoryformatversion = 0
	filemode = false
	bare = false
	logallrefupdates = true
	symlinks = false
	ignorecase = true
[remote "origin"]
	url = **************:galiasystems/smart-charge.git
	fetch = +refs/heads/*:refs/remotes/origin/*
[branch "main"]
	remote = origin
	merge = refs/heads/main
[branch "feature/metrics"]
	remote = origin
	merge = refs/heads/feature/metrics
[branch "feature/metrics_2"]
	remote = origin
	merge = refs/heads/feature/metrics_2
[branch "fix/runtime_init"]
	remote = origin
	merge = refs/heads/fix/runtime_init
[branch "feature/metrics_improvement"]
	remote = origin
	merge = refs/heads/feature/metrics_improvement
[branch "feature/native_image_build"]
	remote = origin
	merge = refs/heads/feature/native_image_build
	vscode-merge-base = origin/main
[branch "feature/sessions_enhancement"]
	remote = origin
	merge = refs/heads/feature/sessions_enhancement
[branch "fix/docker_compose_demo_env"]
	remote = origin
	merge = refs/heads/fix/docker_compose_demo_env
[branch "feature/charging_stations_stuck_in_online_state"]
	remote = origin
	merge = refs/heads/feature/charging_stations_stuck_in_online_state
[branch "feature/report_enhancement"]
	remote = origin
	merge = refs/heads/feature/report_enhancement
